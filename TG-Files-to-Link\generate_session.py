#!/usr/bin/env python3
import os
from dotenv import load_dotenv
from telethon.sync import TelegramClient
from telethon.sessions import StringSession

# Load environment variables
load_dotenv()

API_ID = int(os.environ.get("TG_API_ID", "24161068"))
API_HASH = os.environ.get("TG_API_HASH", "294063699a34d955bdc9a0e3a3e98f97")
BOT_TOKEN = os.environ.get("TG_BOT_FATHER_TOKEN", "7570034044:AAEgOgjW6w5mYsR2k9uWETPbcrsdfQKeow0")

print("Generating session string...")
print(f"API_ID: {API_ID}")
print(f"API_HASH: {API_HASH}")
print(f"BOT_TOKEN: {BOT_TOKEN}")

try:
    with TelegramClient(StringSession(), API_ID, API_HASH) as client:
        # Login with bot token
        client.start(bot_token=BOT_TOKEN)
        session_string = client.session.save()
        print(f"\nSession String: {session_string}")
        
        # Save to .env file
        with open('.env', 'r') as f:
            content = f.read()
        
        if 'TG_SESSION_STRING=' in content:
            # Replace existing session string
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('TG_SESSION_STRING='):
                    lines[i] = f'TG_SESSION_STRING={session_string}'
                    break
            content = '\n'.join(lines)
        else:
            # Add new session string
            content += f'\nTG_SESSION_STRING={session_string}'
        
        with open('.env', 'w') as f:
            f.write(content)
        
        print("Session string saved to .env file!")
        
except Exception as e:
    print(f"Error: {e}")
