# 🎯 Stream Access Control & Persistent Links Features

## 📋 Overview

এই bot এ দুটি নতুন major features যোগ করা হয়েছে:

1. **Stream Access Control System** - নির্দিষ্ট users দের streaming access দেওয়ার জন্য
2. **Persistent File ID Based Links** - File re-upload হলেও links কাজ করবে

---

## 🔐 Stream Access Control System

### ✨ Features:
- শুধুমাত্র authorized users রা streaming করতে পারবে
- অন্য users শুধু download করতে পারবে
- Admin commands দিয়ে users manage করা যাবে
- Database এবং config file দুভাবেই users add করা যায়

### 🛠️ Configuration:

**Environment Variables:**
```bash
# Stream access control চালু/বন্ধ করার জন্য
STREAM_ACCESS_CONTROL=True

# Predefined stream access users (space separated)
STREAM_ACCESS_USERS="********* *********"

# Access denied message (Bengali)
STREAM_ACCESS_MESSAGE="<b>🚫 স্ট্রিম অ্যাক্সেস সীমিত</b>

<b>দুঃখিত! আপনার স্ট্রিমিং অ্যাক্সেস নেই।</b>

<b>📥 আপনি শুধু ফাইল ডাউনলোড করতে পারবেন।</b>

<b>💬 স্ট্রিমিং অ্যাক্সেসের জন্য অ্যাডমিনের সাথে যোগাযোগ করুন।</b>"
```

### 📝 Admin Commands:

#### User Management:
```bash
/add_stream *********        # User কে stream access দিন
/remove_stream *********     # User এর stream access সরান
/check_stream *********      # User এর stream access check করুন
```

#### Statistics & Management:
```bash
/stream_users               # সব stream access users দেখুন
/stream_status             # Stream access control status দেখুন
```

### 🎯 How It Works:

1. **Access Check Priority:**
   - Admins সবসময় access পাবে
   - `STREAM_ACCESS_USERS` list এর users access পাবে
   - Database এ added users access পাবে
   - বাকি সবাই শুধু download করতে পারবে

2. **User Experience:**
   - Stream access থাকলে: Download + Stream buttons
   - Stream access না থাকলে: শুধু Download button + Access denied message

---

## 🔗 Persistent File ID Based Links

### 🎯 Problem Solved:
- আগে message ID দিয়ে links তৈরি হতো
- File delete করে আবার upload করলে message ID পরিবর্তন হয়ে যেত
- পুরানো links আর কাজ করত না

### ✨ Solution:
- এখন Telegram file ID দিয়ে links তৈরি হয়
- File ID সবসময় same থাকে
- File re-upload করলেও links কাজ করবে

### 🔧 Technical Implementation:

#### Database Schema:
```javascript
{
  file_id: "BAADBAADGwADBREAAR8X...",    // Telegram file ID
  message_id: 12345,                      // Current message ID
  file_name: "movie.mp4",                 // File name
  file_size: 1073741824,                  // File size in bytes
  mime_type: "video/mp4",                 // MIME type
  created_at: "2024-01-01T00:00:00Z",     // Creation time
  updated_at: "2024-01-02T00:00:00Z"      // Last update time
}
```

#### Link Format:
```bash
# Old format (message ID based):
https://yourbot.com/12345/movie.mp4?hash=abc123
https://yourbot.com/watch/12345/movie.mp4?hash=abc123

# New format (file ID based):
https://yourbot.com/f_BAADBAADGwADBREAAR8X.../movie.mp4?hash=abc123
https://yourbot.com/watch/f_BAADBAADGwADBREAAR8X.../movie.mp4?hash=abc123
```

### 📝 File Management Commands:

#### Statistics:
```bash
/file_stats                 # File mapping statistics দেখুন
```

#### Maintenance:
```bash
/cleanup_mappings          # Invalid mappings clean করুন
/check_file file_id        # Specific file mapping check করুন
/regenerate_mapping 1000 2000  # Message range থেকে mappings regenerate করুন
```

### 🔄 Auto File Mapping:
- Log channel এ file upload হলে automatically mapping তৈরি হয়
- Same file আবার upload হলে mapping update হয়
- Persistent links সবসময় কাজ করে

---

## 🚀 Usage Examples

### Stream Access Control:

```bash
# Admin commands:
/add_stream *********
# Response: ✅ স্ট্রিম অ্যাক্সেস যোগ করা হয়েছে!

/stream_status
# Response: 📊 স্ট্রিম অ্যাক্সেস স্ট্যাটাস
#          নিয়ন্ত্রণ: 🟢 চালু
#          মোট ইউজার: 15
```

### File Management:

```bash
# Check file mapping:
/check_file BAADBAADGwADBREAAR8X...
# Response: 🔍 ফাইল ম্যাপিং তথ্য
#          ফাইল আইডি: BAADBAADGwADBREAAR8X...
#          মেসেজ আইডি: 12345
#          স্ট্যাটাস: ✅ সক্রিয়

# File statistics:
/file_stats
# Response: 📊 ফাইল ম্যাপিং পরিসংখ্যান
#          মোট ফাইল: 1,234
#          মোট সাইজ: 500 GB
```

---

## 🔧 Installation & Setup

### 1. Database Functions:
নতুন database functions automatically added:
- `add_stream_user()`
- `remove_stream_user()`
- `has_stream_access()`
- `add_file_mapping()`
- `get_file_mapping()`

### 2. New Files Added:
- `neil/plugins/stream_access.py` - Stream access management
- `neil/plugins/file_manager.py` - File mapping management

### 3. Modified Files:
- `neil/info.py` - Configuration variables
- `neil/database/users_chats_db.py` - Database functions
- `neil/plugins/pm_filter.py` - Stream access logic
- `neil/plugins/route.py` - File ID based routing

### 4. Environment Setup:
```bash
# Add to your .env file:
STREAM_ACCESS_CONTROL=True
STREAM_ACCESS_USERS="********* *********"
STREAM_ACCESS_MESSAGE="Your custom access denied message"
```

---

## 🎉 Benefits

### Stream Access Control:
- ✅ Better user management
- ✅ Controlled streaming access
- ✅ Reduced server load
- ✅ Premium user experience

### Persistent Links:
- ✅ Links never break
- ✅ Better user experience
- ✅ Reliable file sharing
- ✅ Automatic file management

---

## 🔮 Future Enhancements

### Possible Additions:
- Time-based stream access (expire after X days)
- Download limit per user
- Bandwidth monitoring
- File access analytics
- Bulk user management
- API endpoints for external management

---

## 🆘 Support

### Common Issues:

1. **Links not working:**
   - Check if file mapping exists: `/check_file file_id`
   - Regenerate mappings: `/regenerate_mapping start_id end_id`

2. **Stream access not working:**
   - Check user access: `/check_stream user_id`
   - Verify configuration: `/stream_status`

3. **Database issues:**
   - Clean invalid mappings: `/cleanup_mappings`
   - Check file statistics: `/file_stats`

### Debug Commands:
```bash
/stream_status      # Check stream access system status
/file_stats         # Check file mapping system status
/check_stream ID    # Check specific user access
/check_file ID      # Check specific file mapping
```

---

## 📞 Contact

For support and questions:
- Telegram: @YourSupportBot
- GitHub Issues: Create an issue in the repository

---

**🎯 এই features গুলো আপনার bot কে আরো professional এবং user-friendly করে তুলবে!**
