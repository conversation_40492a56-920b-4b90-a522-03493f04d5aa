#!/usr/bin/env python3
# Test configuration

try:
    from configs import Config
    print("✅ Config imported successfully")
    
    print(f"API_ID: {Config.API_ID}")
    print(f"API_HASH: {Config.API_HASH}")
    print(f"BOT_TOKEN: {Config.BOT_TOKEN[:10]}...")
    print(f"BOT_USERNAME: {Config.BOT_USERNAME}")
    print(f"DB_CHANNEL: {Config.DB_CHANNEL}")
    print(f"BOT_OWNER: {Config.BOT_OWNER}")
    print(f"DATABASE_URL: {Config.DATABASE_URL[:20]}...")
    
    print("\n✅ All configurations loaded successfully!")
    
except Exception as e:
    print(f"❌ Error loading config: {e}")
    import traceback
    traceback.print_exc()
