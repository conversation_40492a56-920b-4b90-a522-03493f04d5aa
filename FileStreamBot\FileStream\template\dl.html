<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta property="og:image" content="https://www.flaticon.com/premium-icon/icons/svg/2626/2626281.svg" itemprop="thumbnailUrl">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{file_name}}</title>
    <link rel="stylesheet" type='text/css' href="https://drive.google.com/uc?export=view&id=1pVLG4gZy7jdow3sO-wFS06aP_A9QX0O6">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Raleway">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Delius">
</head>

<body class='cyber'>
    <header>
        <div class="toogle"></div>
        <div id="file-name" class="cyber">
            {{file_name}}
        </div>
    </header>

    <div class="container">
        <a href="{{file_url}}">
            <button class="cybr-btn">
                Download
                <span aria-hidden>_</span>
                <span aria-hidden class="cybr-btn__glitch">_404 Error</span>
                <span aria-hidden class="cybr-btn__tag">_%s</span>
            </button>
        </a>
    </div>
	<script>
        const body = document.querySelector('body');
        const title = document.querySelector('#file-name');
        const footer = document.querySelector('footer');
        const toogle = document.querySelector('.toogle');
        toogle.onclick = () => {
            body.classList.toggle('dark')
            footer.classList.toggle('dark')
            title.classList.toggle('dark')
        }
    </script>
</body>
</html>