<p align="center">
  <img src="https://i.ibb.co/dJ0gpJf1/photo-2025-06-16-12-07-05-7516517596376596504.jpg" alt="VJ-FILE-STORE-BOT Logo">
</p>
<h1 align="center">
  VJ FILE STORE BOT
</h1>

![Typing SVG](https://readme-typing-svg.herokuapp.com/?lines=Welcome+To+VJ-FILE-STORE-BOT;A+Highly+Advance+File+Store+Bot;Made+By+Yt-@Tech_VJ!;With+Clone+Feature+Stream/Download+Link;Custom+Url+Shortner+Auto+Delete+Feature;A+Bot+With+Fully+Advanced+Feature!;Thank+You!)
</p>

### Deploy Tutorial [Video Link](https://youtu.be/VxAn9VcYtQg)

## Features

<b><details><summary>Tap On Me For Bot Features</summary>
 
- [x] Permanent Link By Using Website [ Premium Feature] 
- [x] Clone Feature Added [ Premium Feature] 
- [x] Token Verification Feature 
- [x] Stram Feature Added With Many Player Support
- [x] Custom Url Shortner Support Any User Can Add His Own Shortner
- [x] Batch Support Added, Any User Can Use Batch By Making Bot Admin In His File Store Channel
- [x] Auto Delete Feature Added
- [x] Custom Start Message With Picture And Buttons
</b>
</details>

## Environment Variables

<b><details><summary>Tap On Me For Environment Variable</summary>

- `API_ID` : Get From [my.telegram.org](https://my.telegram.org)
- `API_HASH` : Get From [my.telegram.org](https://my.telegram.org)
- `BOT_TOKEN` : Get From [BotFather](https://telegram.me/BotFather)
- `BOT_USERNAME` : Your Bot Username Without @
- `DB_URI` : Mongodb Database Url For Main Bot [Tutorial Watch Here](https://youtu.be/DAHRmFdw99o)
- `CLONE_DB_URI` : Mongodb Database Url For Clone Bot [Tutorial Watch Here](https://youtu.be/DAHRmFdw99o)
- `ADMINS` : It mean Admin/Owner Id For Broadcasting Message.
- `LOG_CHANNEL` : Log channel id start with -100xxxxxx
- `URL` : Your Server App Link With https:// and in last make sure one / is given.
- `AUTO_DELETE` : Time In Minutes
- `AUTO_DELETE_TIME` : Time In Seconds
- `PYTHON_VERSION` : This Variable Is Only For Render, Value IS `3.10.8`
- `PORT` : This Variable Is Only For Render, Value IS `8080`
</b>
</details>

## See How Bot Look Like

<b><details><summary>Tap On Me For Demo Bot</summary></b>

<img src="https://graph.org/file/bb9c59043c52072e8dc93.jpg" alt="Bot Demo">
<img src="https://graph.org/file/295e41dfab93acf42a111.jpg" alt="Bot Demo">
<img src="https://graph.org/file/ccc1b6ab4967a7d155ab8.jpg" alt="Bot Demo">
<img src="https://graph.org/file/75db5257c39436b734b49.jpg" alt="Bot Demo">
<img src="https://graph.org/file/1ce62a17012ed5723aaca.jpg" alt="Bot Demo">
</details>

## Command To Use Bot

<b><details><summary>Tap On Me For Bot Commands</summary>

🖍️ Main Bot Commands :-

- `/start` : By This Command You Can Check Bot Is Alive Or Not
- `/link` : By This Command You Can Generate A Shareable Link Of File By Replying This Command To That File
- `/batch` : By This Command You Can Generate Multiple File Shareable Link At A Time [Use Like This /batch (first post link) (last post link)]
- `/base_site` : By This Command You Can Set Your Url Shortner Domain [Use Like This /base_site domain.com]
- `/api` : By This Command You Can Set Your Url Shortner Api [Use Like This /api (your api key)]
- `/deletecloned` : By This Command You Can Delete Your Cloned Bot [Use Like This /deletecloned (your bot token)]
- `/broadcast` : By Using This Command You Can Broadcast A Message To Your Bot User, Reply This Command To Broadcast Message [Owner Only Command]

🖍️ Clone Bot Commands :- 

- `/start` : By This Command You Can Check Bot Is Alive Or Not
- `/link` : By This Command You Can Generate A Shareable Link Of File By Replying This Command To That File
- `/base_site` : By This Command You Can Set Your Url Shortner Domain [Use Like This /base_site domain.com]
- `/api` : By This Command You Can Set Your Url Shortner Api [Use Like This /api (your api key)]
- `/broadcast` : By Using This Command You Can Broadcast A Message To Your Bot User, Reply This Command To Broadcast Message [Clone Bot Owner Only Command]

</b>
</details>

## Credit

<b><details><summary>Tap On Me For See Credit</summary>

💝 Credit Goes To [Tech VJ](https://telegram.me/Kingvj01)

🖍️ This Code Is Fully Written Or Coded And Public By [Tech VJ](https://telegram.me/Kingvj01) So Don't Forgot To Give Credit

💖 And Thank You So Much To All Who Help In This Journey 💕

Copyright ©️ [Tech VJ](https://telegram.me/Kingvj01)

</b>
</details>

## About Owner 

<b><details><summary>Tap On Me For See Details Of Owner</summary>

- YouTube Channel : [Tech VJ](https://youtube.com/@Tech_VJ)
- Telegram Channel : [VJ Botz](https://telegram.me/VJ_Botz)
- Contact Link : [King VJ](https://telegram.me/Kingvj01)
- Instagram Id Link : [Tech VJ](https://instagram.com/tech.vj)

</b>
</details>


### Copyright ©️ [Tech VJ](https://telegram.me/Kingvj01)

<b>Selling This Repo Or Code Of This Repo For Money Is Strictly Prohibited 🚫</b>

