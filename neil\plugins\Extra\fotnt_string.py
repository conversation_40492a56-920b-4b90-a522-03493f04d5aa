# Don't Remove Credit @VJ_Botz
# Subscribe YouTube Channel For Amazing Bot @Tech_VJ
# Ask <PERSON><PERSON><PERSON> on telegram @KingVJ01


class Fonts:
    def typewriter(text):
        style = {
            'a': '𝚊',
            'b': '𝚋',
            'c': '𝚌',
            'd': '𝚍',
            'e': '𝚎',
            'f': '𝚏',
            'g': '𝚐',
            'h': '𝚑',
            'i': '𝚒',
            'j': '𝚓',
            'k': '𝚔',
            'l': '𝚕',
            'm': '𝚖',
            'n': '𝚗',
            'o': '𝚘',
            'p': '𝚙',
            'q': '𝚚',
            'r': '𝚛',
            's': '𝚜',
            't': '𝚝',
            'u': '𝚞',
            'v': '𝚟',
            'w': '𝚠',
            'x': '𝚡',
            'y': '𝚢',
            'z': '𝚣',
            'A': '𝙰',
            'B': '𝙱',
            'C': '𝙲',
            'D': '𝙳',
            'E': '𝙴',
            'F': '𝙵',
            'G': '𝙶',
            'H': '𝙷',
            'I': '𝙸',
            'J': '𝙹',
            'K': '𝙺',
            'L': '𝙻',
            'M': '𝙼',
            'N': '𝙽',
            'O': '𝙾',
            'P': '𝙿',
            'Q': '𝚀',
            'R': '𝚁',
            'S': '𝚂',
            'T': '𝚃',
            'U': '𝚄',
            'V': '𝚅',
            'W': '𝚆',
            'X': '𝚇',
            'Y': '𝚈',
            'Z': '𝚉'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def outline(text):
        style = {
            'a': '𝕒',
            'b': '𝕓',
            'c': '𝕔',
            'd': '𝕕',
            'e': '𝕖',
            'f': '𝕗',
            'g': '𝕘',
            'h': '𝕙',
            'i': '𝕚',
            'j': '𝕛',
            'k': '𝕜',
            'l': '𝕝',
            'm': '𝕞',
            'n': '𝕟',
            'o': '𝕠',
            'p': '𝕡',
            'q': '𝕢',
            'r': '𝕣',
            's': '𝕤',
            't': '𝕥',
            'u': '𝕦',
            'v': '𝕧',
            'w': '𝕨',
            'x': '𝕩',
            'y': '𝕪',
            'z': '𝕫',
            'A': '𝔸',
            'B': '𝔹',
            'C': 'ℂ',
            'D': '𝔻',
            'E': '𝔼',
            'F': '𝔽',
            'G': '𝔾',
            'H': 'ℍ',
            'I': '𝕀',
            'J': '𝕁',
            'K': '𝕂',
            'L': '𝕃',
            'M': '𝕄',
            'N': 'ℕ',
            'O': '𝕆',
            'P': 'ℙ',
            'Q': 'ℚ',
            'R': 'ℝ',
            'S': '𝕊',
            'T': '𝕋',
            'U': '𝕌',
            'V': '𝕍',
            'W': '𝕎',
            'X': '𝕏',
            'Y': '𝕐',
            'Z': 'ℤ',
            '0': '𝟘',
            '1': '𝟙',
            '2': '𝟚',
            '3': '𝟛',
            '4': '𝟜',
            '5': '𝟝',
            '6': '𝟞',
            '7': '𝟟',
            '8': '𝟠',
            '9': '𝟡'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def serief(text):
        style = {
            'a': '𝐚',
            'b': '𝐛',
            'c': '𝐜',
            'd': '𝐝',
            'e': '𝐞',
            'f': '𝐟',
            'g': '𝐠',
            'h': '𝐡',
            'i': '𝐢',
            'j': '𝐣',
            'k': '𝐤',
            'l': '𝐥',
            'm': '𝐦',
            'n': '𝐧',
            'o': '𝐨',
            'p': '𝐩',
            'q': '𝐪',
            'r': '𝐫',
            's': '𝐬',
            't': '𝐭',
            'u': '𝐮',
            'v': '𝐯',
            'w': '𝐰',
            'x': '𝐱',
            'y': '𝐲',
            'z': '𝐳',
            'A': '𝐀',
            'B': '𝐁',
            'C': '𝐂',
            'D': '𝐃',
            'E': '𝐄',
            'F': '𝐅',
            'G': '𝐆',
            'H': '𝐇',
            'I': '𝐈',
            'J': '𝐉',
            'K': '𝐊',
            'L': '𝐋',
            'M': '𝐌',
            'N': '𝐍',
            'O': '𝐎',
            'P': '𝐏',
            'Q': '𝐐',
            'R': '𝐑',
            'S': '𝐒',
            'T': '𝐓',
            'U': '𝐔',
            'V': '𝐕',
            'W': '𝐖',
            'X': '𝐗',
            'Y': '𝐘',
            'Z': '𝐙',
            '0': '𝟎',
            '1': '𝟏',
            '2': '𝟐',
            '3': '𝟑',
            '4': '𝟒',
            '5': '𝟓',
            '6': '𝟔',
            '7': '𝟕',
            '8': '𝟖',
            '9': '𝟗'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def bold_cool(text):
        style = {
            'a': '𝒂',
            'b': '𝒃',
            'c': '𝒄',
            'd': '𝒅',
            'e': '𝒆',
            'f': '𝒇',
            'g': '𝒈',
            'h': '𝒉',
            'i': '𝒊',
            'j': '𝒋',
            'k': '𝒌',
            'l': '𝒍',
            'm': '𝒎',
            'n': '𝒏',
            'o': '𝒐',
            'p': '𝒑',
            'q': '𝒒',
            'r': '𝒓',
            's': '𝒔',
            't': '𝒕',
            'u': '𝒖',
            'v': '𝒗',
            'w': '𝒘',
            'x': '𝒙',
            'y': '𝒚',
            'z': '𝒛',
            'A': '𝑨',
            'B': '𝑩',
            'C': '𝑪',
            'D': '𝑫',
            'E': '𝑬',
            'F': '𝑭',
            'G': '𝑮',
            'H': '𝑯',
            'I': '𝑰',
            'J': '𝑱',
            'K': '𝑲',
            'L': '𝑳',
            'M': '𝑴',
            'N': '𝑵',
            'O': '𝑶',
            'P': '𝑷',
            'Q': '𝑸',
            'R': '𝑹',
            'S': '𝑺',
            'T': '𝑻',
            'U': '𝑼',
            'V': '𝑽',
            'W': '𝑾',
            'X': '𝑿',
            'Y': '𝒀',
            'Z': '𝒁'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def cool(text):
        style = {
            'a': '𝑎',
            'b': '𝑏',
            'c': '𝑐',
            'd': '𝑑',
            'e': '𝑒',
            'f': '𝑓',
            'g': '𝑔',
            'h': 'ℎ',
            'i': '𝑖',
            'j': '𝑗',
            'k': '𝑘',
            'l': '𝑙',
            'm': '𝑚',
            'n': '𝑛',
            'o': '𝑜',
            'p': '𝑝',
            'q': '𝑞',
            'r': '𝑟',
            's': '𝑠',
            't': '𝑡',
            'u': '𝑢',
            'v': '𝑣',
            'w': '𝑤',
            'x': '𝑥',
            'y': '𝑦',
            'z': '𝑧',
            'A': '𝐴',
            'B': '𝐵',
            'C': '𝐶',
            'D': '𝐷',
            'E': '𝐸',
            'F': '𝐹',
            'G': '𝐺',
            'H': '𝐻',
            'I': '𝐼',
            'J': '𝐽',
            'K': '𝐾',
            'L': '𝐿',
            'M': '𝑀',
            'N': '𝑁',
            'O': '𝑂',
            'P': '𝑃',
            'Q': '𝑄',
            'R': '𝑅',
            'S': '𝑆',
            'T': '𝑇',
            'U': '𝑈',
            'V': '𝑉',
            'W': '𝑊',
            'X': '𝑋',
            'Y': '𝑌',
            'Z': '𝑍'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def smallcap(text):
        style = {
            'a': 'ᴀ',
            'b': 'ʙ',
            'c': 'ᴄ',
            'd': 'ᴅ',
            'e': 'ᴇ',
            'f': 'ғ',
            'g': 'ɢ',
            'h': 'ʜ',
            'i': 'ɪ',
            'j': 'ɪ',
            'k': 'ᴋ',
            'l': 'ʟ',
            'm': 'ᴍ',
            'n': 'ɴ',
            'o': 'ᴏ',
            'p': 'ᴘ',
            'q': 'ǫ',
            'r': 'ʀ',
            's': 's',
            't': 'ᴛ',
            'u': 'ᴜ',
            'v': 'ᴠ',
            'w': 'ᴡ',
            'x': 'x',
            'y': 'ʏ',
            'z': 'ᴢ',
            'A': 'A',
            'B': 'B',
            'C': 'C',
            'D': 'D',
            'E': 'E',
            'F': 'F',
            'G': 'G',
            'H': 'H',
            'I': 'I',
            'J': 'J',
            'K': 'K',
            'L': 'L',
            'M': 'M',
            'N': 'N',
            'O': 'O',
            'P': 'P',
            'Q': 'Q',
            'R': 'R',
            'S': 'S',
            'T': 'T',
            'U': 'U',
            'V': 'V',
            'W': 'W',
            'X': 'X',
            'Y': 'Y',
            'Z': 'Z',
            '0': '𝟶',
            '1': '𝟷',
            '2': '𝟸',
            '3': '𝟹',
            '4': '𝟺',
            '5': '𝟻',
            '6': '𝟼',
            '7': '𝟽',
            '8': '𝟾',
            '9': '𝟿'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def script(text):
        style = {
            'a': '𝒶',
            'b': '𝒷',
            'c': '𝒸',
            'd': '𝒹',
            'e': 'ℯ',
            'f': '𝒻',
            'g': 'ℊ',
            'h': '𝒽',
            'i': '𝒾',
            'j': '𝒿',
            'k': '𝓀',
            'l': '𝓁',
            'm': '𝓂',
            'n': '𝓃',
            'o': 'ℴ',
            'p': '𝓅',
            'q': '𝓆',
            'r': '𝓇',
            's': '𝓈',
            't': '𝓉',
            'u': '𝓊',
            'v': '𝓋',
            'w': '𝓌',
            'x': '𝓍',
            'y': '𝓎',
            'z': '𝓏',
            'A': '𝒜',
            'B': 'ℬ',
            'C': '𝒞',
            'D': '𝒟',
            'E': 'ℰ',
            'F': 'ℱ',
            'G': '𝒢',
            'H': 'ℋ',
            'I': 'ℐ',
            'J': '𝒥',
            'K': '𝒦',
            'L': 'ℒ',
            'M': 'ℳ',
            'N': '𝒩',
            'O': '𝒪',
            'P': '𝒫',
            'Q': '𝒬',
            'R': 'ℛ',
            'S': '𝒮',
            'T': '𝒯',
            'U': '𝒰',
            'V': '𝒱',
            'W': '𝒲',
            'X': '𝒳',
            'Y': '𝒴',
            'Z': '𝒵'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def bold_script(text):
        style = {
            'a': '𝓪',
            'b': '𝓫',
            'c': '𝓬',
            'd': '𝓭',
            'e': '𝓮',
            'f': '𝓯',
            'g': '𝓰',
            'h': '𝓱',
            'i': '𝓲',
            'j': '𝓳',
            'k': '𝓴',
            'l': '𝓵',
            'm': '𝓶',
            'n': '𝓷',
            'o': '𝓸',
            'p': '𝓹',
            'q': '𝓺',
            'r': '𝓻',
            's': '𝓼',
            't': '𝓽',
            'u': '𝓾',
            'v': '𝓿',
            'w': '𝔀',
            'x': '𝔁',
            'y': '𝔂',
            'z': '𝔃',
            'A': '𝓐',
            'B': '𝓑',
            'C': '𝓒',
            'D': '𝓓',
            'E': '𝓔',
            'F': '𝓕',
            'G': '𝓖',
            'H': '𝓗',
            'I': '𝓘',
            'J': '𝓙',
            'K': '𝓚',
            'L': '𝓛',
            'M': '𝓜',
            'N': '𝓝',
            'O': '𝓞',
            'P': '𝓟',
            'Q': '𝓠',
            'R': '𝓡',
            'S': '𝓢',
            'T': '𝓣',
            'U': '𝓤',
            'V': '𝓥',
            'W': '𝓦',
            'X': '𝓧',
            'Y': '𝓨',
            'Z': '𝓩'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def tiny(text):
        style = {
            'a': 'ᵃ',
            'b': 'ᵇ',
            'c': 'ᶜ',
            'd': 'ᵈ',
            'e': 'ᵉ',
            'f': 'ᶠ',
            'g': 'ᵍ',
            'h': 'ʰ',
            'i': 'ⁱ',
            'j': 'ʲ',
            'k': 'ᵏ',
            'l': 'ˡ',
            'm': 'ᵐ',
            'n': 'ⁿ',
            'o': 'ᵒ',
            'p': 'ᵖ',
            'q': 'ᵠ',
            'r': 'ʳ',
            's': 'ˢ',
            't': 'ᵗ',
            'u': 'ᵘ',
            'v': 'ᵛ',
            'w': 'ʷ',
            'x': 'ˣ',
            'y': 'ʸ',
            'z': 'ᶻ',
            'A': 'ᵃ',
            'B': 'ᵇ',
            'C': 'ᶜ',
            'D': 'ᵈ',
            'E': 'ᵉ',
            'F': 'ᶠ',
            'G': 'ᵍ',
            'H': 'ʰ',
            'I': 'ⁱ',
            'J': 'ʲ',
            'K': 'ᵏ',
            'L': 'ˡ',
            'M': 'ᵐ',
            'N': 'ⁿ',
            'O': 'ᵒ',
            'P': 'ᵖ',
            'Q': 'ᵠ',
            'R': 'ʳ',
            'S': 'ˢ',
            'T': 'ᵗ',
            'U': 'ᵘ',
            'V': 'ᵛ',
            'W': 'ʷ',
            'X': 'ˣ',
            'Y': 'ʸ',
            'Z': 'ᶻ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def comic(text):
        style = {
            'a': 'ᗩ',
            'b': 'ᗷ',
            'c': 'ᑕ',
            'd': 'ᗪ',
            'e': 'ᗴ',
            'f': 'ᖴ',
            'g': 'ᘜ',
            'h': 'ᕼ',
            'i': 'I',
            'j': 'ᒍ',
            'k': 'K',
            'l': 'ᒪ',
            'm': 'ᗰ',
            'n': 'ᑎ',
            'o': 'O',
            'p': 'ᑭ',
            'q': 'ᑫ',
            'r': 'ᖇ',
            's': 'Տ',
            't': 'T',
            'u': 'ᑌ',
            'v': 'ᐯ',
            'w': 'ᗯ',
            'x': '᙭',
            'y': 'Y',
            'z': 'ᘔ',
            'A': 'ᗩ',
            'B': 'ᗷ',
            'C': 'ᑕ',
            'D': 'ᗪ',
            'E': 'ᗴ',
            'F': 'ᖴ',
            'G': 'ᘜ',
            'H': 'ᕼ',
            'I': 'I',
            'J': 'ᒍ',
            'K': 'K',
            'L': 'ᒪ',
            'M': 'ᗰ',
            'N': 'ᑎ',
            'O': 'O',
            'P': 'ᑭ',
            'Q': 'ᑫ',
            'R': 'ᖇ',
            'S': 'Տ',
            'T': 'T',
            'U': 'ᑌ',
            'V': 'ᐯ',
            'W': 'ᗯ',
            'X': '᙭',
            'Y': 'Y',
            'Z': 'ᘔ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def san(text):
        style = {
            'a': '𝗮',
            'b': '𝗯',
            'c': '𝗰',
            'd': '𝗱',
            'e': '𝗲',
            'f': '𝗳',
            'g': '𝗴',
            'h': '𝗵',
            'i': '𝗶',
            'j': '𝗷',
            'k': '𝗸',
            'l': '𝗹',
            'm': '𝗺',
            'n': '𝗻',
            'o': '𝗼',
            'p': '𝗽',
            'q': '𝗾',
            'r': '𝗿',
            's': '𝘀',
            't': '𝘁',
            'u': '𝘂',
            'v': '𝘃',
            'w': '𝘄',
            'x': '𝘅',
            'y': '𝘆',
            'z': '𝘇',
            'A': '𝗔',
            'B': '𝗕',
            'C': '𝗖',
            'D': '𝗗',
            'E': '𝗘',
            'F': '𝗙',
            'G': '𝗚',
            'H': '𝗛',
            'I': '𝗜',
            'J': '𝗝',
            'K': '𝗞',
            'L': '𝗟',
            'M': '𝗠',
            'N': '𝗡',
            'O': '𝗢',
            'P': '𝗣',
            'Q': '𝗤',
            'R': '𝗥',
            'S': '𝗦',
            'T': '𝗧',
            'U': '𝗨',
            'V': '𝗩',
            'W': '𝗪',
            'X': '𝗫',
            'Y': '𝗬',
            'Z': '𝗭',
            '0': '𝟬',
            '1': '𝟭',
            '2': '𝟮',
            '3': '𝟯',
            '4': '𝟰',
            '5': '𝟱',
            '6': '𝟲',
            '7': '𝟳',
            '8': '𝟴',
            '9': '𝟵'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def slant_san(text):
        style = {
            'a': '𝙖',
            'b': '𝙗',
            'c': '𝙘',
            'd': '𝙙',
            'e': '𝙚',
            'f': '𝙛',
            'g': '𝙜',
            'h': '𝙝',
            'i': '𝙞',
            'j': '𝙟',
            'k': '𝙠',
            'l': '𝙡',
            'm': '𝙢',
            'n': '𝙣',
            'o': '𝙤',
            'p': '𝙥',
            'q': '𝙦',
            'r': '𝙧',
            's': '𝙨',
            't': '𝙩',
            'u': '𝙪',
            'v': '𝙫',
            'w': '𝙬',
            'x': '𝙭',
            'y': '𝙮',
            'z': '𝙯',
            'A': '𝘼',
            'B': '𝘽',
            'C': '𝘾',
            'D': '𝘿',
            'E': '𝙀',
            'F': '𝙁',
            'G': '𝙂',
            'H': '𝙃',
            'I': '𝙄',
            'J': '𝙅',
            'K': '𝙆',
            'L': '𝙇',
            'M': '𝙈',
            'N': '𝙉',
            'O': '𝙊',
            'P': '𝙋',
            'Q': '𝙌',
            'R': '𝙍',
            'S': '𝙎',
            'T': '𝙏',
            'U': '𝙐',
            'V': '𝙑',
            'W': '𝙒',
            'X': '𝙓',
            'Y': '𝙔',
            'Z': '𝙕'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def slant(text):
        style = {
            'a': '𝘢',
            'b': '𝘣',
            'c': '𝘤',
            'd': '𝘥',
            'e': '𝘦',
            'f': '𝘧',
            'g': '𝘨',
            'h': '𝘩',
            'i': '𝘪',
            'j': '𝘫',
            'k': '𝘬',
            'l': '𝘭',
            'm': '𝘮',
            'n': '𝘯',
            'o': '𝘰',
            'p': '𝘱',
            'q': '𝘲',
            'r': '𝘳',
            's': '𝘴',
            't': '𝘵',
            'u': '𝘶',
            'v': '𝘷',
            'w': '𝘸',
            'x': '𝘹',
            'y': '𝘺',
            'z': '𝘻',
            'A': '𝘈',
            'B': '𝘉',
            'C': '𝘊',
            'D': '𝘋',
            'E': '𝘌',
            'F': '𝘍',
            'G': '𝘎',
            'H': '𝘏',
            'I': '𝘐',
            'J': '𝘑',
            'K': '𝘒',
            'L': '𝘓',
            'M': '𝘔',
            'N': '𝘕',
            'O': '𝘖',
            'P': '𝘗',
            'Q': '𝘘',
            'R': '𝘙',
            'S': '𝘚',
            'T': '𝘛',
            'U': '𝘜',
            'V': '𝘝',
            'W': '𝘞',
            'X': '𝘟',
            'Y': '𝘠',
            'Z': '𝘡'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def sim(text):
        style = {
            'a': '𝖺',
            'b': '𝖻',
            'c': '𝖼',
            'd': '𝖽',
            'e': '𝖾',
            'f': '𝖿',
            'g': '𝗀',
            'h': '𝗁',
            'i': '𝗂',
            'j': '𝗃',
            'k': '𝗄',
            'l': '𝗅',
            'm': '𝗆',
            'n': '𝗇',
            'o': '𝗈',
            'p': '𝗉',
            'q': '𝗊',
            'r': '𝗋',
            's': '𝗌',
            't': '𝗍',
            'u': '𝗎',
            'v': '𝗏',
            'w': '𝗐',
            'x': '𝗑',
            'y': '𝗒',
            'z': '𝗓',
            'A': '𝖠',
            'B': '𝖡',
            'C': '𝖢',
            'D': '𝖣',
            'E': '𝖤',
            'F': '𝖥',
            'G': '𝖦',
            'H': '𝖧',
            'I': '𝖨',
            'J': '𝖩',
            'K': '𝖪',
            'L': '𝖫',
            'M': '𝖬',
            'N': '𝖭',
            'O': '𝖮',
            'P': '𝖯',
            'Q': '𝖰',
            'R': '𝖱',
            'S': '𝖲',
            'T': '𝖳',
            'U': '𝖴',
            'V': '𝖵',
            'W': '𝖶',
            'X': '𝖷',
            'Y': '𝖸',
            'Z': '𝖹'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def circles(text):
        style = {
            'a': 'Ⓐ︎',
            'b': 'Ⓑ︎',
            'c': 'Ⓒ︎',
            'd': 'Ⓓ︎',
            'e': 'Ⓔ︎',
            'f': 'Ⓕ︎',
            'g': 'Ⓖ︎',
            'h': 'Ⓗ︎',
            'i': 'Ⓘ︎',
            'j': 'Ⓙ︎',
            'k': 'Ⓚ︎',
            'l': 'Ⓛ︎',
            'm': 'Ⓜ︎',
            'n': 'Ⓝ︎',
            'o': 'Ⓞ︎',
            'p': 'Ⓟ︎',
            'q': 'Ⓠ︎',
            'r': 'Ⓡ︎',
            's': 'Ⓢ︎',
            't': 'Ⓣ︎',
            'u': 'Ⓤ︎',
            'v': 'Ⓥ︎',
            'w': 'Ⓦ︎',
            'x': 'Ⓧ︎',
            'y': 'Ⓨ︎',
            'z': 'Ⓩ︎',
            'A': 'Ⓐ︎',
            'B': 'Ⓑ︎',
            'C': 'Ⓒ︎',
            'D': 'Ⓓ︎',
            'E': 'Ⓔ︎',
            'F': 'Ⓕ︎',
            'G': 'Ⓖ︎',
            'H': 'Ⓗ︎',
            'I': 'Ⓘ︎',
            'J': 'Ⓙ︎',
            'K': 'Ⓚ︎',
            'L': 'Ⓛ︎',
            'M': 'Ⓜ︎',
            'N': 'Ⓝ︎',
            'O': 'Ⓞ︎',
            'P': 'Ⓟ︎',
            'Q': 'Ⓠ︎',
            'R': 'Ⓡ︎',
            'S': 'Ⓢ︎',
            'T': 'Ⓣ︎',
            'U': 'Ⓤ︎',
            'V': 'Ⓥ︎',
            'W': 'Ⓦ︎',
            'X': 'Ⓧ︎',
            'Y': 'Ⓨ︎',
            'Z': 'Ⓩ︎',
            '0': '⓪',
            '1': '①',
            '2': '②',
            '3': '③',
            '4': '④',
            '5': '⑤',
            '6': '⑥',
            '7': '⑦',
            '8': '⑧',
            '9': '⑨'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def dark_circle(text):
        style = {
            'a': '🅐︎',
            'b': '🅑︎',
            'c': '🅒︎',
            'd': '🅓︎',
            'e': '🅔︎',
            'f': '🅕︎',
            'g': '🅖︎',
            'h': '🅗︎',
            'i': '🅘︎',
            'j': '🅙︎',
            'k': '🅚︎',
            'l': '🅛︎',
            'm': '🅜︎',
            'n': '🅝︎',
            'o': '🅞︎',
            'p': '🅟︎',
            'q': '🅠︎',
            'r': '🅡︎',
            's': '🅢︎',
            't': '🅣︎',
            'u': '🅤︎',
            'v': '🅥︎',
            'w': '🅦︎',
            'x': '🅧︎',
            'y': '🅨︎',
            'z': '🅩︎',
            'A': '🅐︎',
            'B': '🅑︎',
            'C': '🅒︎',
            'D': '🅓︎',
            'E': '🅔︎',
            'F': '🅕︎',
            'G': '🅖︎',
            'H': '🅗︎',
            'I': '🅘︎',
            'J': '🅙︎',
            'K': '🅚︎',
            'L': '🅛︎',
            'M': '🅜︎',
            'N': '🅝︎',
            'O': '🅞︎',
            'P': '🅟︎',
            'Q': '🅠︎',
            'R': '🅡︎',
            'S': '🅢︎',
            'T': '🅣︎',
            'U': '🅤︎',
            'V': '🅥︎',
            'W': '🅦︎',
            'X': '🅧︎',
            'Y': '🅨︎',
            'Z': '🅩',
            '0': '⓿',
            '1': '➊',
            '2': '➋',
            '3': '➌',
            '4': '➍',
            '5': '➎',
            '6': '➏',
            '7': '➐',
            '8': '➑',
            '9': '➒'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def gothic(text):
        style = {
            'a': '𝔞',
            'b': '𝔟',
            'c': '𝔠',
            'd': '𝔡',
            'e': '𝔢',
            'f': '𝔣',
            'g': '𝔤',
            'h': '𝔥',
            'i': '𝔦',
            'j': '𝔧',
            'k': '𝔨',
            'l': '𝔩',
            'm': '𝔪',
            'n': '𝔫',
            'o': '𝔬',
            'p': '𝔭',
            'q': '𝔮',
            'r': '𝔯',
            's': '𝔰',
            't': '𝔱',
            'u': '𝔲',
            'v': '𝔳',
            'w': '𝔴',
            'x': '𝔵',
            'y': '𝔶',
            'z': '𝔷',
            'A': '𝔄',
            'B': '𝔅',
            'C': 'ℭ',
            'D': '𝔇',
            'E': '𝔈',
            'F': '𝔉',
            'G': '𝔊',
            'H': 'ℌ',
            'I': 'ℑ',
            'J': '𝔍',
            'K': '𝔎',
            'L': '𝔏',
            'M': '𝔐',
            'N': '𝔑',
            'O': '𝔒',
            'P': '𝔓',
            'Q': '𝔔',
            'R': 'ℜ',
            'S': '𝔖',
            'T': '𝔗',
            'U': '𝔘',
            'V': '𝔙',
            'W': '𝔚',
            'X': '𝔛',
            'Y': '𝔜',
            'Z': 'ℨ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text


    def bold_gothic(text):
        style = {
            'a': '𝖆',
            'b': '𝖇',
            'c': '𝖈',
            'd': '𝖉',
            'e': '𝖊',
            'f': '𝖋',
            'g': '𝖌',
            'h': '𝖍',
            'i': '𝖎',
            'j': '𝖏',
            'k': '𝖐',
            'l': '𝖑',
            'm': '𝖒',
            'n': '𝖓',
            'o': '𝖔',
            'p': '𝖕',
            'q': '𝖖',
            'r': '𝖗',
            's': '𝖘',
            't': '𝖙',
            'u': '𝖚',
            'v': '𝖛',
            'w': '𝖜',
            'x': '𝖝',
            'y': '𝖞',
            'z': '𝖟',
            'A': '𝕬',
            'B': '𝕭',
            'C': '𝕮',
            'D': '𝕺',
            'E': '𝕰',
            'F': '𝕱',
            'G': '𝕲',
            'H': '𝕳',
            'I': '𝕴',
            'J': '𝕵',
            'K': '𝕶',
            'L': '𝕷',
            'M': '𝕸',
            'N': '𝕹',
            'O': '𝕺',
            'P': '𝕻',
            'Q': '𝕼',
            'R': '𝕽',
            'S': '𝕾',
            'T': '𝕿',
            'U': '𝖀',
            'V': '𝖁',
            'W': '𝖂',
            'X': '𝖃',
            'Y': '𝖄',
            'Z': '𝖅'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def cloud(text):
        style = {
            'a': 'a͜͡',
            'b': 'b͜͡',
            'c': 'c͜͡',
            'd': 'd͜͡',
            'e': 'e͜͡',
            'f': 'f͜͡',
            'g': 'g͜͡',
            'h': 'h͜͡',
            'i': 'i͜͡',
            'j': 'j͜͡',
            'k': 'k͜͡',
            'l': 'l͜͡',
            'm': 'm͜͡',
            'n': 'n͜͡',
            'o': 'o͜͡',
            'p': 'p͜͡',
            'q': 'q͜͡',
            'r': 'r͜͡',
            's': 's͜͡',
            't': 't͜͡',
            'u': 'u͜͡',
            'v': 'v͜͡',
            'w': 'w͜͡',
            'x': 'x͜͡',
            'y': 'y͜͡',
            'z': 'z͜͡',
            'A': 'A͜͡',
            'B': 'B͜͡',
            'C': 'C͜͡',
            'D': 'D͜͡',
            'E': 'E͜͡',
            'F': 'F͜͡',
            'G': 'G͜͡',
            'H': 'H͜͡',
            'I': 'I͜͡',
            'J': 'J͜͡',
            'K': 'K͜͡',
            'L': 'L͜͡',
            'M': 'M͜͡',
            'N': 'N͜͡',
            'O': 'O͜͡',
            'P': 'P͜͡',
            'Q': 'Q͜͡',
            'R': 'R͜͡',
            'S': 'S͜͡',
            'T': 'T͜͡',
            'U': 'U͜͡',
            'V': 'V͜͡',
            'W': 'W͜͡',
            'X': 'X͜͡',
            'Y': 'Y͜͡',
            'Z': 'Z͜͡'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def happy(text):
        style = {
            'a': 'ă̈',
            'b': 'b̆̈',
            'c': 'c̆̈',
            'd': 'd̆̈',
            'e': 'ĕ̈',
            'f': 'f̆̈',
            'g': 'ğ̈',
            'h': 'h̆̈',
            'i': 'ĭ̈',
            'j': 'j̆̈',
            'k': 'k̆̈',
            'l': 'l̆̈',
            'm': 'm̆̈',
            'n': 'n̆̈',
            'o': 'ŏ̈',
            'p': 'p̆̈',
            'q': 'q̆̈',
            'r': 'r̆̈',
            's': 's̆̈',
            't': 't̆̈',
            'u': 'ŭ̈',
            'v': 'v̆̈',
            'w': 'w̆̈',
            'x': 'x̆̈',
            'y': 'y̆̈',
            'z': 'z̆̈',
            'A': 'Ă̈',
            'B': 'B̆̈',
            'C': 'C̆̈',
            'D': 'D̆̈',
            'E': 'Ĕ̈',
            'F': 'F̆̈',
            'G': 'Ğ̈',
            'H': 'H̆̈',
            'I': 'Ĭ̈',
            'J': 'J̆̈',
            'K': 'K̆̈',
            'L': 'L̆̈',
            'M': 'M̆̈',
            'N': 'N̆̈',
            'O': 'Ŏ̈',
            'P': 'P̆̈',
            'Q': 'Q̆̈',
            'R': 'R̆̈',
            'S': 'S̆̈',
            'T': 'T̆̈',
            'U': 'Ŭ̈',
            'V': 'V̆̈',
            'W': 'W̆̈',
            'X': 'X̆̈',
            'Y': 'Y̆̈',
            'Z': 'Z̆̈'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def sad(text):
        style = {
            'a': 'ȃ̈',
            'b': 'b̑̈',
            'c': 'c̑̈',
            'd': 'd̑̈',
            'e': 'ȇ̈',
            'f': 'f̑̈',
            'g': 'g̑̈',
            'h': 'h̑̈',
            'i': 'ȋ̈',
            'j': 'j̑̈',
            'k': 'k̑̈',
            'l': 'l̑̈',
            'm': 'm̑̈',
            'n': 'n̑̈',
            'o': 'ȏ̈',
            'p': 'p̑̈',
            'q': 'q̑̈',
            'r': 'ȓ̈',
            's': 's̑̈',
            't': 't̑̈',
            'u': 'ȗ̈',
            'v': 'v̑̈',
            'w': 'w̑̈',
            'x': 'x̑̈',
            'y': 'y̑̈',
            'z': 'z̑̈',
            'A': 'Ȃ̈',
            'B': 'B̑̈',
            'C': 'C̑̈',
            'D': 'D̑̈',
            'E': 'Ȇ̈',
            'F': 'F̑̈',
            'G': 'G̑̈',
            'H': 'H̑̈',
            'I': 'Ȋ̈',
            'J': 'J̑̈',
            'K': 'K̑̈',
            'L': 'L̑̈',
            'M': 'M̑̈',
            'N': 'N̑̈',
            'O': 'Ȏ̈',
            'P': 'P̑̈',
            'Q': 'Q̑̈',
            'R': 'Ȓ̈',
            'S': 'S̑̈',
            'T': 'T̑̈',
            'U': 'Ȗ̈',
            'V': 'V̑̈',
            'W': 'W̑̈',
            'X': 'X̑̈',
            'Y': 'Y̑̈',
            'Z': 'Z̑̈'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def special(text):
        style = {
            'a': '🇦 ',
            'b': '🇧 ',
            'c': '🇨 ',
            'd': '🇩 ',
            'e': '🇪 ',
            'f': '🇫 ',
            'g': '🇬 ',
            'h': '🇭 ',
            'i': '🇮 ',
            'j': '🇯 ',
            'k': '🇰 ',
            'l': '🇱 ',
            'm': '🇲 ',
            'n': '🇳 ',
            'o': '🇴 ',
            'p': '🇵 ',
            'q': '🇶 ',
            'r': '🇷 ',
            's': '🇸 ',
            't': '🇹 ',
            'u': '🇺 ',
            'v': '🇻 ',
            'w': '🇼 ',
            'x': '🇽 ',
            'y': '🇾 ',
            'z': '🇿 ',
            'A': '🇦 ',
            'B': '🇧 ',
            'C': '🇨 ',
            'D': '🇩 ',
            'E': '🇪 ',
            'F': '🇫 ',
            'G': '🇬 ',
            'H': '🇭 ',
            'I': '🇮 ',
            'J': '🇯 ',
            'K': '🇰 ',
            'L': '🇱 ',
            'M': '🇲 ',
            'N': '🇳 ',
            'O': '🇴 ',
            'P': '🇵 ',
            'Q': '🇶 ',
            'R': '🇷 ',
            'S': '🇸 ',
            'T': '🇹 ',
            'U': '🇺 ',
            'V': '🇻 ',
            'W': '🇼 ',
            'X': '🇽 ',
            'Y': '🇾 ',
            'Z': '🇿 '
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def square(text):
        style = {
            'a': '🄰',
            'b': '🄱',
            'c': '🄲',
            'd': '🄳',
            'e': '🄴',
            'f': '🄵',
            'g': '🄶',
            'h': '🄷',
            'i': '🄸',
            'j': '🄹',
            'k': '🄺',
            'l': '🄻',
            'm': '🄼',
            'n': '🄽',
            'o': '🄾',
            'p': '🄿',
            'q': '🅀',
            'r': '🅁',
            's': '🅂',
            't': '🅃',
            'u': '🅄',
            'v': '🅅',
            'w': '🅆',
            'x': '🅇',
            'y': '🅈',
            'z': '🅉',
            'A': '🄰',
            'B': '🄱',
            'C': '🄲',
            'D': '🄳',
            'E': '🄴',
            'F': '🄵',
            'G': '🄶',
            'H': '🄷',
            'I': '🄸',
            'J': '🄹',
            'K': '🄺',
            'L': '🄻',
            'M': '🄼',
            'N': '🄽',
            'O': '🄾',
            'P': '🄿',
            'Q': '🅀',
            'R': '🅁',
            'S': '🅂',
            'T': '🅃',
            'U': '🅄',
            'V': '🅅',
            'W': '🅆',
            'X': '🅇',
            'Y': '🅈',
            'Z': '🅉'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def dark_square(text):
        style = {
            'a': '🅰︎',
            'b': '🅱︎',
            'c': '🅲︎',
            'd': '🅳︎',
            'e': '🅴︎',
            'f': '🅵︎',
            'g': '🅶︎',
            'h': '🅷︎',
            'i': '🅸︎',
            'j': '🅹︎',
            'k': '🅺︎',
            'l': '🅻︎',
            'm': '🅼︎',
            'n': '🅽︎',
            'o': '🅾︎',
            'p': '🅿︎',
            'q': '🆀︎',
            'r': '🆁︎',
            's': '🆂︎',
            't': '🆃︎',
            'u': '🆄︎',
            'v': '🆅︎',
            'w': '🆆︎',
            'x': '🆇︎',
            'y': '🆈︎',
            'z': '🆉︎',
            'A': '🅰︎',
            'B': '🅱︎',
            'C': '🅲︎',
            'D': '🅳︎',
            'E': '🅴︎',
            'F': '🅵︎',
            'G': '🅶︎',
            'H': '🅷︎',
            'I': '🅸︎',
            'J': '🅹︎',
            'K': '🅺︎',
            'L': '🅻︎',
            'M': '🅼︎',
            'N': '🅽︎',
            'O': '🅾︎',
            'P': '🅿︎',
            'Q': '🆀︎',
            'R': '🆁︎',
            'S': '🆂︎',
            'T': '🆃︎',
            'U': '🆄︎',
            'V': '🆅︎',
            'W': '🆆︎',
            'X': '🆇︎',
            'Y': '🆈︎',
            'Z': '🆉︎'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def andalucia(text):
        style = {
            'a': 'ꪖ',
            'b': '᥇',
            'c': 'ᥴ',
            'd': 'ᦔ',
            'e': 'ꫀ',
            'f': 'ᠻ',
            'g': 'ᧁ',
            'h': 'ꫝ',
            'i': '𝓲',
            'j': '𝓳',
            'k': '𝘬',
            'l': 'ꪶ',
            'm': 'ꪑ',
            'n': 'ꪀ',
            'o': 'ꪮ',
            'p': 'ρ',
            'q': '𝘲',
            'r': '𝘳',
            's': '𝘴',
            't': '𝓽',
            'u': 'ꪊ',
            'v': 'ꪜ',
            'w': '᭙',
            'x': '᥊',
            'y': 'ꪗ',
            'z': 'ɀ',
            'A': 'ꪖ',
            'B': '᥇',
            'C': 'ᥴ',
            'D': 'ᦔ',
            'E': 'ꫀ',
            'F': 'ᠻ',
            'G': 'ᧁ',
            'H': 'ꫝ',
            'I': '𝓲',
            'J': '𝓳',
            'K': '𝘬',
            'L': 'ꪶ',
            'M': 'ꪑ',
            'N': 'ꪀ',
            'O': 'ꪮ',
            'P': 'ρ',
            'Q': '𝘲',
            'R': '𝘳',
            'S': '𝘴',
            'T': '𝓽',
            'U': 'ꪊ',
            'V': 'ꪜ',
            'W': '᭙',
            'X': '᥊',
            'Y': 'ꪗ',
            'Z': 'ɀ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def manga(text):
        style = {
            'a': '卂',
            'b': '乃',
            'c': '匚',
            'd': 'ᗪ',
            'e': '乇',
            'f': '千',
            'g': 'ᘜ',
            'h': '卄',
            'i': '|',
            'j': 'ﾌ',
            'k': 'Ҝ',
            'l': 'ㄥ',
            'm': '爪',
            'n': '几',
            'o': 'ㄖ',
            'p': '卩',
            'q': 'Ҩ',
            'r': '尺',
            's': '丂',
            't': 'ㄒ',
            'u': 'ㄩ',
            'v': 'ᐯ',
            'w': '山',
            'x': '乂',
            'y': 'ㄚ',
            'z': '乙',
            'A': '卂',
            'B': '乃',
            'C': '匚',
            'D': 'ᗪ',
            'E': '乇',
            'F': '千',
            'G': 'ᘜ',
            'H': '卄',
            'I': '|',
            'J': 'ﾌ',
            'K': 'Ҝ',
            'L': 'ㄥ',
            'M': '爪',
            'N': '几',
            'O': 'ㄖ',
            'P': '卩',
            'Q': 'Ҩ',
            'R': '尺',
            'S': '丂',
            'T': 'ㄒ',
            'U': 'ㄩ',
            'V': 'ᐯ',
            'W': '山',
            'X': '乂',
            'Y': 'ㄚ',
            'Z': '乙'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def stinky(text):
        style = {
            'a': 'a̾',
            'b': 'b̾',
            'c': 'c̾',
            'd': 'd̾',
            'e': 'e̾',
            'f': 'f̾',
            'g': 'g̾',
            'h': 'h̾',
            'i': 'i̾',
            'j': 'j̾',
            'k': 'k̾',
            'l': 'l̾',
            'm': 'm̾',
            'n': 'n̾',
            'o': 'o̾',
            'p': 'p̾',
            'q': 'q̾',
            'r': 'r̾',
            's': 's̾',
            't': 't̾',
            'u': 'u̾',
            'v': 'v̾',
            'w': 'w̾',
            'x': 'x̾',
            'y': 'y̾',
            'z': 'z̾',
            'A': 'A̾',
            'B': 'B̾',
            'C': 'C̾',
            'D': 'D̾',
            'E': 'E̾',
            'F': 'F̾',
            'G': 'G̾',
            'H': 'H̾',
            'I': 'I̾',
            'J': 'J̾',
            'K': 'K̾',
            'L': 'L̾',
            'M': 'M̾',
            'N': 'N̾',
            'O': 'O̾',
            'P': 'P̾',
            'Q': 'Q̾',
            'R': 'R̾',
            'S': 'S̾',
            'T': 'T̾',
            'U': 'U̾',
            'V': 'V̾',
            'W': 'W̾',
            'X': 'X̾',
            'Y': 'Y̾',
            'Z': 'Z̾'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def bubbles(text):
        style = {
            'a': 'ḁͦ',
            'b': 'b̥ͦ',
            'c': 'c̥ͦ',
            'd': 'd̥ͦ',
            'e': 'e̥ͦ',
            'f': 'f̥ͦ',
            'g': 'g̥ͦ',
            'h': 'h̥ͦ',
            'i': 'i̥ͦ',
            'j': 'j̥ͦ',
            'k': 'k̥ͦ',
            'l': 'l̥ͦ',
            'm': 'm̥ͦ',
            'n': 'n̥ͦ',
            'o': 'o̥ͦ',
            'p': 'p̥ͦ',
            'q': 'q̥ͦ',
            'r': 'r̥ͦ',
            's': 's̥ͦ',
            't': 't̥ͦ',
            'u': 'u̥ͦ',
            'v': 'v̥ͦ',
            'w': 'w̥ͦ',
            'x': 'x̥ͦ',
            'y': 'y̥ͦ',
            'z': 'z̥ͦ',
            'A': 'Ḁͦ',
            'B': 'B̥ͦ',
            'C': 'C̥ͦ',
            'D': 'D̥ͦ',
            'E': 'E̥ͦ',
            'F': 'F̥ͦ',
            'G': 'G̥ͦ',
            'H': 'H̥ͦ',
            'I': 'I̥ͦ',
            'J': 'J̥ͦ',
            'K': 'K̥ͦ',
            'L': 'L̥ͦ',
            'M': 'M̥ͦ',
            'N': 'N̥ͦ',
            'O': 'O̥ͦ',
            'P': 'P̥ͦ',
            'Q': 'Q̥ͦ',
            'R': 'R̥ͦ',
            'S': 'S̥ͦ',
            'T': 'T̥ͦ',
            'U': 'U̥ͦ',
            'V': 'V̥ͦ',
            'W': 'W̥ͦ',
            'X': 'X̥ͦ',
            'Y': 'Y̥ͦ',
            'Z': 'Z̥ͦ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def underline(text):
        style = {
            'a': 'a͟',
            'b': 'b͟',
            'c': 'c͟',
            'd': 'd͟',
            'e': 'e͟',
            'f': 'f͟',
            'g': 'g͟',
            'h': 'h͟',
            'i': 'i͟',
            'j': 'j͟',
            'k': 'k͟',
            'l': 'l͟',
            'm': 'm͟',
            'n': 'n͟',
            'o': 'o͟',
            'p': 'p͟',
            'q': 'q͟',
            'r': 'r͟',
            's': 's͟',
            't': 't͟',
            'u': 'u͟',
            'v': 'v͟',
            'w': 'w͟',
            'x': 'x͟',
            'y': 'y͟',
            'z': 'z͟',
            'A': 'A͟',
            'B': 'B͟',
            'C': 'C͟',
            'D': 'D͟',
            'E': 'E͟',
            'F': 'F͟',
            'G': 'G͟',
            'H': 'H͟',
            'I': 'I͟',
            'J': 'J͟',
            'K': 'K͟',
            'L': 'L͟',
            'M': 'M͟',
            'N': 'N͟',
            'O': 'O͟',
            'P': 'P͟',
            'Q': 'Q͟',
            'R': 'R͟',
            'S': 'S͟',
            'T': 'T͟',
            'U': 'U͟',
            'V': 'V͟',
            'W': 'W͟',
            'X': 'X͟',
            'Y': 'Y͟',
            'Z': 'Z͟'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def ladybug(text):
        style = {
            'a': 'ꍏ',
            'b': 'ꌃ',
            'c': 'ꏳ',
            'd': 'ꀷ',
            'e': 'ꏂ',
            'f': 'ꎇ',
            'g': 'ꁅ',
            'h': 'ꀍ',
            'i': 'ꀤ',
            'j': '꒻',
            'k': 'ꀘ',
            'l': '꒒',
            'm': 'ꎭ',
            'n': 'ꈤ',
            'o': 'ꂦ',
            'p': 'ᖘ',
            'q': 'ꆰ',
            'r': 'ꋪ',
            's': 'ꌚ',
            't': '꓄',
            'u': 'ꀎ',
            'v': '꒦',
            'w': 'ꅐ',
            'x': 'ꉧ',
            'y': 'ꌩ',
            'z': 'ꁴ',
            'A': 'ꍏ',
            'B': 'ꌃ',
            'C': 'ꏳ',
            'D': 'ꀷ',
            'E': 'ꏂ',
            'F': 'ꎇ',
            'G': 'ꁅ',
            'H': 'ꀍ',
            'I': 'ꀤ',
            'J': '꒻',
            'K': 'ꀘ',
            'L': '꒒',
            'M': 'ꎭ',
            'N': 'ꈤ',
            'O': 'ꂦ',
            'P': 'ᖘ',
            'Q': 'ꆰ',
            'R': 'ꋪ',
            'S': 'ꌚ',
            'T': '꓄',
            'U': 'ꀎ',
            'V': '꒦',
            'W': 'ꅐ',
            'X': 'ꉧ',
            'Y': 'ꌩ',
            'Z': 'ꁴ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def rays(text):
        style = {
            'a': 'a҉',
            'b': 'b҉',
            'c': 'c҉',
            'd': 'd҉',
            'e': 'e҉',
            'f': 'f҉',
            'g': 'g҉',
            'h': 'h҉',
            'i': 'i҉',
            'j': 'j҉',
            'k': 'k҉',
            'l': 'l҉',
            'm': 'm҉',
            'n': 'n҉',
            'o': 'o҉',
            'p': 'p҉',
            'q': 'q҉',
            'r': 'r҉',
            's': 's҉',
            't': 't҉',
            'u': 'u҉',
            'v': 'v҉',
            'w': 'w҉',
            'x': 'x҉',
            'y': 'y҉',
            'z': 'z҉',
            'A': 'A҉',
            'B': 'B҉',
            'C': 'C҉',
            'D': 'D҉',
            'E': 'E҉',
            'F': 'F҉',
            'G': 'G҉',
            'H': 'H҉',
            'I': 'I҉',
            'J': 'J҉',
            'K': 'K҉',
            'L': 'L҉',
            'M': 'M҉',
            'N': 'N҉',
            'O': 'O҉',
            'P': 'P҉',
            'Q': 'Q҉',
            'R': 'R҉',
            'S': 'S҉',
            'T': 'T҉',
            'U': 'U҉',
            'V': 'V҉',
            'W': 'W҉',
            'X': 'X҉',
            'Y': 'Y҉',
            'Z': 'Z҉'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def birds(text):
        style = {
            'a': 'a҈',
            'b': 'b҈',
            'c': 'c҈',
            'd': 'd҈',
            'e': 'e҈',
            'f': 'f҈',
            'g': 'g҈',
            'h': 'h҈',
            'i': 'i҈',
            'j': 'j҈',
            'k': 'k҈',
            'l': 'l҈',
            'm': 'm҈',
            'n': 'n҈',
            'o': 'o҈',
            'p': 'p҈',
            'q': 'q҈',
            'r': 'r҈',
            's': 's҈',
            't': 't҈',
            'u': 'u҈',
            'v': 'v҈',
            'w': 'w҈',
            'x': 'x҈',
            'y': 'y҈',
            'z': 'z҈',
            'A': 'A҈',
            'B': 'B҈',
            'C': 'C҈',
            'D': 'D҈',
            'E': 'E҈',
            'F': 'F҈',
            'G': 'G҈',
            'H': 'H҈',
            'I': 'I҈',
            'J': 'J҈',
            'K': 'K҈',
            'L': 'L҈',
            'M': 'M҈',
            'N': 'N҈',
            'O': 'O҈',
            'P': 'P҈',
            'Q': 'Q҈',
            'R': 'R҈',
            'S': 'S҈',
            'T': 'T҈',
            'U': 'U҈',
            'V': 'V҈',
            'W': 'W҈',
            'X': 'X҈',
            'Y': 'Y҈',
            'Z': 'Z҈'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def slash(text):
        style = {
            'a': 'a̸',
            'b': 'b̸',
            'c': 'c̸',
            'd': 'd̸',
            'e': 'e̸',
            'f': 'f̸',
            'g': 'g̸',
            'h': 'h̸',
            'i': 'i̸',
            'j': 'j̸',
            'k': 'k̸',
            'l': 'l̸',
            'm': 'm̸',
            'n': 'n̸',
            'o': 'o̸',
            'p': 'p̸',
            'q': 'q̸',
            'r': 'r̸',
            's': 's̸',
            't': 't̸',
            'u': 'u̸',
            'v': 'v̸',
            'w': 'w̸',
            'x': 'x̸',
            'y': 'y̸',
            'z': 'z̸',
            'A': 'A̸',
            'B': 'B̸',
            'C': 'C̸',
            'D': 'D̸',
            'E': 'E̸',
            'F': 'F̸',
            'G': 'G̸',
            'H': 'H̸',
            'I': 'I̸',
            'J': 'J̸',
            'K': 'K̸',
            'L': 'L̸',
            'M': 'M̸',
            'N': 'N̸',
            'O': 'O̸',
            'P': 'P̸',
            'Q': 'Q̸',
            'R': 'R̸',
            'S': 'S̸',
            'T': 'T̸',
            'U': 'U̸',
            'V': 'V̸',
            'W': 'W̸',
            'X': 'X̸',
            'Y': 'Y̸',
            'Z': 'Z̸'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def stop(text):
        style = {
            'a': 'a⃠',
            'b': 'b⃠',
            'c': 'c⃠',
            'd': 'd⃠',
            'e': 'e⃠',
            'f': 'f⃠',
            'g': 'g⃠',
            'h': 'h⃠',
            'i': 'i⃠',
            'j': 'j⃠',
            'k': 'k⃠',
            'l': 'l⃠',
            'm': 'm⃠',
            'n': 'n⃠',
            'o': 'o⃠',
            'p': 'p⃠',
            'q': 'q⃠',
            'r': 'r⃠',
            's': 's⃠',
            't': 't⃠',
            'u': 'u⃠',
            'v': 'v⃠',
            'w': 'w⃠',
            'x': 'x⃠',
            'y': 'y⃠',
            'z': 'z⃠',
            'A': 'A⃠',
            'B': 'B⃠',
            'C': 'C⃠',
            'D': 'D⃠',
            'E': 'E⃠',
            'F': 'F⃠',
            'G': 'G⃠',
            'H': 'H⃠',
            'I': 'I⃠',
            'J': 'J⃠',
            'K': 'K⃠',
            'L': 'L⃠',
            'M': 'M⃠',
            'N': 'N⃠',
            'O': 'O⃠',
            'P': 'P⃠',
            'Q': 'Q⃠',
            'R': 'R⃠',
            'S': 'S⃠',
            'T': 'T⃠',
            'U': 'U⃠',
            'V': 'V⃠',
            'W': 'W⃠',
            'X': 'X⃠',
            'Y': 'Y⃠',
            'Z': 'Z⃠'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def skyline(text):
        style = {
            'a': 'a̺͆',
            'b': 'b̺͆',
            'c': 'c̺͆',
            'd': 'd̺͆',
            'e': 'e̺͆',
            'f': 'f̺͆',
            'g': 'g̺͆',
            'h': 'h̺͆',
            'i': 'i̺͆',
            'j': 'j̺͆',
            'k': 'k̺͆',
            'l': 'l̺͆',
            'm': 'm̺͆',
            'n': 'n̺͆',
            'o': 'o̺͆',
            'p': 'p̺͆',
            'q': 'q̺͆',
            'r': 'r̺͆',
            's': 's̺͆',
            't': 't̺͆',
            'u': 'u̺͆',
            'v': 'v̺͆',
            'w': 'w̺͆',
            'x': 'x̺͆',
            'y': 'y̺͆',
            'z': 'z̺͆',
            'A': 'A̺͆',
            'B': 'B̺͆',
            'C': 'C̺͆',
            'D': 'D̺͆',
            'E': 'E̺͆',
            'F': 'F̺͆',
            'G': 'G̺͆',
            'H': 'H̺͆',
            'I': 'I̺͆',
            'J': 'J̺͆',
            'K': 'K̺͆',
            'L': 'L̺͆',
            'M': 'M̺͆',
            'N': 'N̺͆',
            'O': 'O̺͆',
            'P': 'P̺͆',
            'Q': 'Q̺͆',
            'R': 'R̺͆',
            'S': 'S̺͆',
            'T': 'T̺͆',
            'U': 'U̺͆',
            'V': 'V̺͆',
            'W': 'W̺͆',
            'X': 'X̺͆',
            'Y': 'Y̺͆',
            'Z': 'Z̺͆'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def arrows(text):
        style = {
            'a': 'a͎',
            'b': 'b͎',
            'c': 'c͎',
            'd': 'd͎',
            'e': 'e͎',
            'f': 'f͎',
            'g': 'g͎',
            'h': 'h͎',
            'i': 'i͎',
            'j': 'j͎',
            'k': 'k͎',
            'l': 'l͎',
            'm': 'm͎',
            'n': 'n͎',
            'o': 'o͎',
            'p': 'p͎',
            'q': 'q͎',
            'r': 'r͎',
            's': 's͎',
            't': 't͎',
            'u': 'u͎',
            'v': 'v͎',
            'w': 'w͎',
            'x': 'x͎',
            'y': 'y͎',
            'z': 'z͎',
            'A': 'A͎',
            'B': 'B͎',
            'C': 'C͎',
            'D': 'D͎',
            'E': 'E͎',
            'F': 'F͎',
            'G': 'G͎',
            'H': 'H͎',
            'I': 'I͎',
            'J': 'J͎',
            'K': 'K͎',
            'L': 'L͎',
            'M': 'M͎',
            'N': 'N͎',
            'O': 'O͎',
            'P': 'P͎',
            'Q': 'Q͎',
            'R': 'R͎',
            'S': 'S͎',
            'T': 'T͎',
            'U': 'U͎',
            'V': 'V͎',
            'W': 'W͎',
            'X': 'X͎',
            'Y': 'Y͎',
            'Z': 'Z͎'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def rvnes(text):
        style = {
            'a': 'ል',
            'b': 'ጌ',
            'c': 'ር',
            'd': 'ዕ',
            'e': 'ቿ',
            'f': 'ቻ',
            'g': 'ኗ',
            'h': 'ዘ',
            'i': 'ጎ',
            'j': 'ጋ',
            'k': 'ጕ',
            'l': 'ረ',
            'm': 'ጠ',
            'n': 'ክ',
            'o': 'ዐ',
            'p': 'የ',
            'q': 'ዒ',
            'r': 'ዪ',
            's': 'ነ',
            't': 'ፕ',
            'u': 'ሁ',
            'v': 'ሀ',
            'w': 'ሠ',
            'x': 'ሸ',
            'y': 'ሃ',
            'z': 'ጊ',
            'A': 'ል',
            'B': 'ጌ',
            'C': 'ር',
            'D': 'ዕ',
            'E': 'ቿ',
            'F': 'ቻ',
            'G': 'ኗ',
            'H': 'ዘ',
            'I': 'ጎ',
            'J': 'ጋ',
            'K': 'ጕ',
            'L': 'ረ',
            'M': 'ጠ',
            'N': 'ክ',
            'O': 'ዐ',
            'P': 'የ',
            'Q': 'ዒ',
            'R': 'ዪ',
            'S': 'ነ',
            'T': 'ፕ',
            'U': 'ሁ',
            'V': 'ሀ',
            'W': 'ሠ',
            'X': 'ሸ',
            'Y': 'ሃ',
            'Z': 'ጊ'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def strike(text):
        style = {
            'a': 'a̶',
            'b': 'b̶',
            'c': 'c̶',
            'd': 'd̶',
            'e': 'e̶',
            'f': 'f̶',
            'g': 'g̶',
            'h': 'h̶',
            'i': 'i̶',
            'j': 'j̶',
            'k': 'k̶',
            'l': 'l̶',
            'm': 'm̶',
            'n': 'n̶',
            'o': 'o̶',
            'p': 'p̶',
            'q': 'q̶',
            'r': 'r̶',
            's': 's̶',
            't': 't̶',
            'u': 'u̶',
            'v': 'v̶',
            'w': 'w̶',
            'x': 'x̶',
            'y': 'y̶',
            'z': 'z̶',
            'A': 'A̶',
            'B': 'B̶',
            'C': 'C̶',
            'D': 'D̶',
            'E': 'E̶',
            'F': 'F̶',
            'G': 'G̶',
            'H': 'H̶',
            'I': 'I̶',
            'J': 'J̶',
            'K': 'K̶',
            'L': 'L̶',
            'M': 'M̶',
            'N': 'N̶',
            'O': 'O̶',
            'P': 'P̶',
            'Q': 'Q̶',
            'R': 'R̶',
            'S': 'S̶',
            'T': 'T̶',
            'U': 'U̶',
            'V': 'V̶',
            'W': 'W̶',
            'X': 'X̶',
            'Y': 'Y̶',
            'Z': 'Z̶'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text

    def frozen(text):
        style = {
            'a': 'a༙',
            'b': 'b༙',
            'c': 'c༙',
            'd': 'd༙',
            'e': 'e༙',
            'f': 'f༙',
            'g': 'g༙',
            'h': 'h༙',
            'i': 'i༙',
            'j': 'j༙',
            'k': 'k༙',
            'l': 'l༙',
            'm': 'm༙',
            'n': 'n༙',
            'o': 'o༙',
            'p': 'p༙',
            'q': 'q༙',
            'r': 'r༙',
            's': 's༙',
            't': 't༙',
            'u': 'u༙',
            'v': 'v༙',
            'w': 'w༙',
            'x': 'x༙',
            'y': 'y༙',
            'z': 'z༙',
            'A': 'A༙',
            'B': 'B༙',
            'C': 'C༙',
            'D': 'D༙',
            'E': 'E༙',
            'F': 'F༙',
            'G': 'G༙',
            'H': 'H༙',
            'I': 'I༙',
            'J': 'J༙',
            'K': 'K༙',
            'L': 'L༙',
            'M': 'M༙',
            'N': 'N༙',
            'O': 'O༙',
            'P': 'P༙',
            'Q': 'Q༙',
            'R': 'R༙',
            'S': 'S༙',
            'T': 'T༙',
            'U': 'U༙',
            'V': 'V༙',
            'W': 'W༙',
            'X': 'X༙',
            'Y': 'Y༙',
            'Z': 'Z༙'
        }
        for i, j in style.items():
            text = text.replace(i, j)
        return text
