# 🧪 Test Plan for Stream Access Control & Persistent Links

## 📋 Pre-Testing Checklist

### ✅ Code Review Completed:
- [x] All imports are correct
- [x] Database collections properly configured
- [x] No syntax errors detected
- [x] Function signatures match usage
- [x] Environment variables properly referenced

### 🔧 Configuration Required:

#### Environment Variables (.env):
```bash
# Add these to your .env file:
STREAM_ACCESS_CONTROL=True
STREAM_ACCESS_USERS="********* *********"  # Replace with actual user IDs
STREAM_ACCESS_MESSAGE="<b>🚫 স্ট্রিম অ্যাক্সেস সীমিত</b>

<b>দুঃখিত! আপনার স্ট্রিমিং অ্যাক্সেস নেই।</b>

<b>📥 আপনি শুধু ফাইল ডাউনলোড করতে পারবেন।</b>

<b>💬 স্ট্রিমিং অ্যাক্সেসের জন্য অ্যাডমিনের সাথে যোগাযোগ করুন।</b>"
```

---

## 🎯 Test Scenarios

### 1. Stream Access Control Testing

#### Test 1.1: Admin Access
**Steps:**
1. Admin user sends a file request
2. Click "Get Stream Link" button
3. **Expected:** Both Download + Stream buttons appear
4. **Expected:** No access denied message

#### Test 1.2: Authorized User Access
**Steps:**
1. Add user to stream access: `/add_stream USER_ID`
2. User sends a file request
3. Click "Get Stream Link" button
4. **Expected:** Both Download + Stream buttons appear

#### Test 1.3: Unauthorized User Access
**Steps:**
1. Non-authorized user sends a file request
2. Click "Get Stream Link" button
3. **Expected:** Only Download button appears
4. **Expected:** Access denied message shows

#### Test 1.4: Admin Commands
**Commands to test:**
```bash
/add_stream *********        # Should add user
/remove_stream *********     # Should remove user
/check_stream *********      # Should show user status
/stream_users               # Should list all users
/stream_status             # Should show system status
```

### 2. Persistent Links Testing

#### Test 2.1: File Upload & Link Generation
**Steps:**
1. Upload a file to log channel
2. Request stream link
3. **Expected:** Link format: `https://yourbot.com/f_BAADBAADGwAD.../filename.ext`
4. **Expected:** File mapping created in database

#### Test 2.2: Link Functionality
**Steps:**
1. Copy generated persistent link
2. Open link in browser
3. **Expected:** File streams/downloads successfully
4. **Expected:** No 404 errors

#### Test 2.3: File Re-upload Persistence
**Steps:**
1. Upload file → Get link → Test link (should work)
2. Delete the file from log channel
3. Re-upload the SAME file
4. Test the OLD link
5. **Expected:** Old link still works with new message

#### Test 2.4: File Management Commands
**Commands to test:**
```bash
/file_stats                 # Should show file statistics
/check_file FILE_ID         # Should show file mapping info
/cleanup_mappings          # Should clean invalid mappings
/regenerate_mapping 1000 2000  # Should regenerate mappings
```

### 3. Database Integration Testing

#### Test 3.1: Stream Access Database
**Verify:**
- Users added via `/add_stream` are stored in database
- Users removed via `/remove_stream` are deleted from database
- `has_stream_access()` function works correctly
- Environment variable users are recognized

#### Test 3.2: File Mapping Database
**Verify:**
- File mappings are created on upload
- File mappings are updated on re-upload
- File mappings can be retrieved by file ID
- Invalid mappings can be cleaned up

### 4. Error Handling Testing

#### Test 4.1: Invalid File ID
**Steps:**
1. Try to access: `https://yourbot.com/f_INVALID_FILE_ID/test.mp4`
2. **Expected:** "File mapping not found" error

#### Test 4.2: Missing Message
**Steps:**
1. Create file mapping with non-existent message ID
2. Try to access the file
3. **Expected:** Appropriate error handling

#### Test 4.3: Database Connection Issues
**Steps:**
1. Temporarily disconnect database
2. Try stream access commands
3. **Expected:** Graceful error messages

---

## 🔍 Manual Testing Steps

### Phase 1: Basic Functionality
1. **Start the bot**
2. **Test admin commands:**
   ```bash
   /stream_status
   /file_stats
   ```
3. **Upload a test file to log channel**
4. **Request stream link as admin**
5. **Verify both download and stream buttons appear**

### Phase 2: Stream Access Control
1. **Add a test user:**
   ```bash
   /add_stream USER_ID
   ```
2. **Test with authorized user account**
3. **Remove user access:**
   ```bash
   /remove_stream USER_ID
   ```
4. **Test with unauthorized user account**
5. **Verify access denied message appears**

### Phase 3: Persistent Links
1. **Upload file → Get persistent link**
2. **Test link in browser**
3. **Delete file from log channel**
4. **Re-upload same file**
5. **Test old link again**
6. **Verify it still works**

### Phase 4: File Management
1. **Check file mapping:**
   ```bash
   /check_file FILE_ID
   ```
2. **View file statistics:**
   ```bash
   /file_stats
   ```
3. **Clean up invalid mappings:**
   ```bash
   /cleanup_mappings
   ```

---

## 🚨 Common Issues & Solutions

### Issue 1: Links not working
**Possible causes:**
- File mapping not created
- Wrong file ID format
- Database connection issues

**Debug steps:**
```bash
/check_file FILE_ID
/file_stats
```

### Issue 2: Stream access not working
**Possible causes:**
- Environment variables not set
- Database functions not working
- User ID mismatch

**Debug steps:**
```bash
/stream_status
/check_stream USER_ID
```

### Issue 3: Database errors
**Possible causes:**
- MongoDB connection issues
- Collection name conflicts
- Permission issues

**Debug steps:**
- Check MongoDB logs
- Verify database URI
- Test database connection

---

## 📊 Success Criteria

### ✅ Stream Access Control:
- [ ] Admins can access streaming
- [ ] Authorized users can access streaming
- [ ] Unauthorized users see access denied
- [ ] Admin commands work correctly
- [ ] Database operations successful

### ✅ Persistent Links:
- [ ] File ID based links generated
- [ ] Links work immediately after creation
- [ ] Links survive file re-upload
- [ ] File mappings stored correctly
- [ ] Management commands functional

### ✅ Integration:
- [ ] No conflicts with existing features
- [ ] Performance impact minimal
- [ ] Error handling robust
- [ ] User experience smooth

---

## 🎯 Performance Testing

### Load Testing:
1. **Upload 100+ files**
2. **Generate persistent links for all**
3. **Test multiple simultaneous access**
4. **Monitor database performance**

### Memory Testing:
1. **Monitor bot memory usage**
2. **Check for memory leaks**
3. **Test with large files**

---

## 📝 Test Results Template

```
Date: ___________
Tester: ___________

Stream Access Control:
[ ] Admin access - PASS/FAIL
[ ] Authorized user access - PASS/FAIL  
[ ] Unauthorized user access - PASS/FAIL
[ ] Admin commands - PASS/FAIL

Persistent Links:
[ ] Link generation - PASS/FAIL
[ ] Link functionality - PASS/FAIL
[ ] File re-upload persistence - PASS/FAIL
[ ] File management - PASS/FAIL

Database Integration:
[ ] Stream access DB - PASS/FAIL
[ ] File mapping DB - PASS/FAIL
[ ] Error handling - PASS/FAIL

Overall Status: PASS/FAIL
Notes: ___________
```

---

## 🚀 Deployment Checklist

Before going live:
- [ ] All tests passed
- [ ] Environment variables configured
- [ ] Database backup taken
- [ ] Documentation updated
- [ ] Admin users notified
- [ ] Monitoring setup

**🎉 Ready for production deployment!**
