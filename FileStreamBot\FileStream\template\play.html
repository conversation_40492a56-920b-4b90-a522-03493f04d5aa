<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FileStreamBot | {{file_name}}</title>
    <link rel="icon" href="https://i.ibb.co/Hh4kF2b/icon.png" type="image/x-icon">
    <link rel="shortcut icon" href="https://i.ibb.co/Hh4kF2b/icon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://unpkg.com/sheryjs/dist/Shery.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/proavipatil/data@main/fs/src/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/proavipatil/data@main/fs/src/plyr.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>

</head>

<body>
    <nav>
        <div class="nleft">
            <a href="#">
                <h3 id="heading" style="z-index: 100;" class="magnet title">FILE STREAM</h3>
            </a>
        </div>
        <div class="nryt">
            <a class="home-btn magnet" href="#main" onclick="toggleWidthnav(this)">HOME</a>

            <a href="#abtus" class="about-btn magnet" onclick="toggleWidthnav(this)">ABOUT</a>
        </div>
    </nav>
    <center>
        <div class="about-nav">
            <a href="#abtus" class="wlcm magnet" onclick="toggleWidth(this)">WELCOME</a>
            <a href="#channels" class="abt-chnl magnet" onclick="toggleWidth(this)">CHANNELS</a>
            <a href="#contact" class="magnet contact-btn" onclick="toggleWidth(this)">CONTACT</a>
        </div>
    </center>

    <div class="outer">
        <div class="inner">
            <div class="main" id="main">
                <video id="player" class="player" src="{{file_url}}" type="video/mp4" playsinline controls
                    width="100%"></video>
                <div class="player"></div>
                <div class="file-name">
                    <h4 style="display: inline;">File Name: </h4>
                    <p style="display: inline;" id="myDiv">{{file_name}}</p><br>
                    <h4 style="display: inline;">File Size: </h4>
                    <p style="display: inline;">{{file_size}}</p>
                </div>
                <div class="downloadBtn">
                    <button class="magnet" onclick="streamDownload()">
                        <img style="height: 30px;" src="https://i.ibb.co/RjzYttX/dl.png" alt="">download video
                    </button>
                    <button class="magnet" onclick="copyStreamLink()">
                        <img src="https://i.ibb.co/CM4Y586/link.png" alt="Copy Link">copy link
                    </button>
                    <button class="magnet" onclick="vlc_player()">
                        <img src="https://i.ibb.co/px6fQs1/vlc.png" alt="">watch in VLC PLAYER
                    </button>
                    <button class="magnet" onclick="mx_player()">
                        <img src="https://i.ibb.co/41WvtQ3/mx.png" alt="">watch in MX PLAYER
                    </button>
                    <button class="magnet" onclick="n_player()">
                        <img src="https://i.ibb.co/Hd2dS4t/nPlayer.png" alt="">watch in nPlayer
                    </button>
                </div>

            </div>
            <div class="abt">
                <div class="about">
                    <div class="about-dets">

                        <div class="abt-sec" id="abtus" style="padding: 160px 30px;">
                            <h1 style="text-align: center;">WELCOME TO OUR <Span>FILE STREAM</Span> BOT</h1>
                            <p style="text-align: center; line-height: 2;word-spacing: 2px;  letter-spacing: 0.8px;">
                                This is a Telegram Bot to Stream <span>Movies</span> and <span>Series</span> directly on
                                Telegram. You can also
                                <span>download</span> them if you want. This bot is developed by <a
                                    href="https://github.com/AviPatilPro"><span style="font-weight: 700;">Avi</span></a>
                                <br><br>If you like this bot, then don't
                                forget to share it with your friends and family.
                            </p>
                        </div>

                        <div class="abt-sec" id="channels">
                            <h1>JOIN OUR <span>TELEGRAM</span> CHANNELS</h1>
                            <div class="links chnl-link">
                                <a class="magnet" href="https://t.me/CheapieDeals">
                                    <button>CHEAP DEALS</button>
                                </a>
                                <a class="magnet" href="https://t.me/FilmyPixel">
                                    <button>FILMYPIXEL</button>
                                </a>
                                <a class="magnet" href="https://t.me/PostersZone">
                                    <button>POSTERS ZONE</button>
                                </a>
                                <a class="magnet" href="https://t.me/EpitomeQuality">
                                    <button>EPITOME QUALITY</button>
                                </a>
                            </div>
                        </div>

                        <div class="abt-sec" id="contact">
                            <p style="text-align: center;">Report Bugs and Contact us on Telegram Below</p>
                            <div class="links contact">
                                <a href="https://t.me/AvishkarPatil">
                                    <button>CONTACT</button>
                                </a>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <footer>
            <center>

                <div class="movie-cont">
                    <div class="movieSug">
                        <div class="movieDets">

                        </div>
                        <div class="movieimg">
                            <img src="">
                        </div>
                    </div>
                    <div class="low-width-movie-dets">
                        <div class="movieStsBar">
                            <div class="movieDets-mini">

                            </div>
                        </div>
                    </div>
                    <button class="ranMovBtn" onclick="getDets()">Get More Movies Suggestion</button>
                </div>

            </center>

            <center>
                <div class="copyright">
                    <h5 class="text-center">Copyright © 2024 <a href="https://github.com/AviPatilPro"><span
                                style="font-weight: 700;">Avishkar Patil</span></a>. All
                        Rights Reserved.</h5>
                </div>
            </center>
        </footer>
    </div>
</body>

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.155.0/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/gh/automat/controlkit.js@master/bin/controlKit.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/sheryjs/dist/Shery.js"></script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const uncopyableElement = document.querySelector(".uncopyable");

        uncopyableElement.addEventListener("selectstart", function (event) {
            event.preventDefault();
        });
    });
</script>
<script src="https://cdn.plyr.io/3.6.9/plyr.js"></script>
<script src="https://proavipatil.github.io/data/fs/src/script.js"></script>

</html>