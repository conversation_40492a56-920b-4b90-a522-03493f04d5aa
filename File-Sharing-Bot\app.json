{"name": "TG File Share/Sharing Bot", "description": "file sharing bot store posts and it can access by special links", "keywords": ["telegram", "file", "sharing"], "repository": "https://github.com/CodeXBotz/File-Sharing-Bot", "logo": "https://ibb.co/FgPVtzw", "env": {"TG_BOT_TOKEN": {"description": "Your <PERSON><PERSON> token, Get it from @Botfather", "value": ""}, "OWNER_ID": {"description": "An integer of consisting of your owner ID", "value": "1250450587"}, "APP_ID": {"description": "your app id, take it from my.telegram.org", "value": ""}, "DATABASE_URL": {"description": "Paste your mongo db url", "value": "url"}, "DATABASE_NAME": {"description": "Enter your DATABASE_NAME ", "value": "filesharexbot"}, "API_HASH": {"description": "your api hash, take it from my.telegram.org", "value": ""}, "CHANNEL_ID": {"description": "make a channel (database channel), then make the bot as admin in channel, and it's id", "value": "-100"}, "FORCE_SUB_CHANNEL": {"description": "id of the channel or group, if you want enable force sub feature else put 0", "value": "0"}, "START_MESSAGE": {"description": "Optional: start message of bot, use HTML parsemode format", "value": "Hello {first}\n\nI can store private files in Specified Channel and other users can access it from special link."}, "START_PIC": {"description": "Optional: URL or file path of the image to be sent as the start message,", "value": " "}, "FORCE_SUB_MESSAGE": {"description": "Optional: Force Sub message of bot, use HTML parsemode format", "value": "Hello {first}\n\n<b>You need to join in my Channel/Group to use me\n\nKindly Please join Channel</b>"}, "ADMINS": {"description": "A space separated list of user_ids of Admins, they can only create links", "value": "", "required": false}, "PROTECT_CONTENT": {"description": "Protect contents from getting forwarded", "value": "False", "required": false}, "AUTO_DELETE_TIME": {"description": "Set the time in seconds for the automatic deletion of the file. Default is 0, meaning auto-deletion is disabled unless specified.", "value": "0"}, "AUTO_DELETE_MSG": {"description": "The custom message shown to the user before the file is automatically deleted. You can include a countdown with {time}.", "value": "This file will be automatically deleted in {time} seconds. Please ensure you have saved any necessary content before this time."}, "AUTO_DEL_SUCCESS_MSG": {"description": "The custom message shown to the user once the file is successfully deleted.", "value": "Your file has been successfully deleted. Thank you for using our service. ✅"}}, "buildpacks": [{"url": "heroku/python"}], "formation": {"worker": {"quantity": 1, "size": "eco"}}}