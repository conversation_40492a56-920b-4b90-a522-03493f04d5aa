<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon" href="https://i.ibb.co.com/Qw2PbsF/cinepix.png" type="image/x-icon">
    <meta property="og:image" content="https://i.ibb.co.com/Qw2PbsF/cinepix.png" itemprop="thumbnailUrl">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{file_name}} | neil_admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>
    <link rel="stylesheet" type='text/css' href="https://drive----google.com/uc?export=view&id=1pVLG4gZy7jdow3sO-wFS06aP_A9QX0O6">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Raleway">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Delius">
    <link rel="stylesheet" href="https://cdn.plyr.io/3.6.9/plyr.css" />
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-black">
    <header>
        <!-- Navbar -->
            <nav class="navbar bg-dark p-lg-4 p-2">
               <div class="d-flex ">
                 <!-- Sidebar Menu -> Open Button -->
                 <button class="btn border border-danger-subtle wow animate__animated animate__fadeInLeft animate__delay-1s" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasWithBothOptions" aria-controls="offcanvasWithBothOptions">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="#fff" class="bi bi-menu-button-wide-fill" viewBox="0 0 16 16"><path fill="url(#PMWUiMSnUf2R_PPJ6rqZea_44112_gr1)" d="M1.5 0A1.5 1.5 0 0 0 0 1.5v2A1.5 1.5 0 0 0 1.5 5h13A1.5 1.5 0 0 0 16 3.5v-2A1.5 1.5 0 0 0 14.5 0h-13zm1 2h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1 0-1zm9.927.427A.25.25 0 0 1 12.604 2h.792a.25.25 0 0 1 .177.427l-.396.396a.25.25 0 0 1-.354 0l-.396-.396zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2H1zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2h14zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z"/></svg>
                </button>
                <!-- sidebar menu -->
                <div class="offcanvas offcanvas-start bg-dark-subtle " data-bs-scroll="true" tabindex="-1" id="offcanvasWithBothOptions" aria-labelledby="offcanvasWithBothOptionsLabel">
                <div class="offcanvas-header d-block">
                    <div class="justify-content-end align-items-end d-flex">
                        <button type="button bg-dark " class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                    </div>
                    <div class="d-flex ">
                        <!-- logo -->
                        <img src="https://i.ibb.co.com/Qw2PbsF/cinepix.png" width="80" height="80" class="rounded" style="box-shadow: 3px 5px 11px black;" alt="">
                        <div class="p-3">
                            <h4 class="offcanvas-title font-semibold wow animate__animated animate__fadeInLeft " id="offcanvasWithBothOptionsLabel" style="text-shadow: 3px 5px 11px black;" >neil_admin</h4>
                            <p class="text-red-600 font-semibold wow animate__animated animate__fadeIn animate__delay-1s animate__infinite">The One & Only</p>
                        </div>
                    </div>
                    <a href="https://github.com/neil" target="_blank" class="btn btn-dark w-100 text-light fw-semibold flex justify-center items-center gap-2 mt-2 wow animate__animated animate__fadeInDown  ">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-github" viewBox="0 0 16 16">
                            <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"/>
                          </svg>    
                        Follow neil_admin
                    </a>
                    <div class="d-flex gap-1 px-1 mt-1 wow animate__animated animate__fadeInLeft animate__delay-1s ">
                        <a href="https://teleram.me/neil_admin" target="_blank" class="btn btn-secondary w-50 text-light fw-semibold flex justify-center items-center gap-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-telegram" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.287 5.906c-.778.324-2.334.994-4.666 2.01-.378.15-.577.298-.595.442-.03.243.275.339.69.47l.175.055c.408.133.958.288 1.243.294.26.006.549-.1.868-.32 2.179-1.471 3.304-2.214 3.374-2.23.05-.012.12-.026.166.016.047.041.042.12.037.141-.03.129-1.227 1.241-1.846 1.817-.193.18-.33.307-.358.336a8.154 8.154 0 0 1-.188.186c-.38.366-.664.64.015 1.088.327.216.589.393.85.571.284.194.568.387.936.629.093.06.183.125.27.187.331.236.63.448.997.414.214-.02.435-.22.547-.82.265-1.417.786-4.486.906-5.751a1.426 1.426 0 0 0-.013-.315.337.337 0 0 0-.114-.217.526.526 0 0 0-.31-.093c-.3.005-.763.166-2.984 1.09z"/>
                              </svg>
                            Updates
                        </a>
                        <a href="https://github.com/neil" target="_blank" class="btn btn-primary w-50 text-light  fw-semibold flex justify-center items-center gap-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-git" viewBox="0 0 16 16">
                                <path d="M15.698 7.287 8.712.302a1.03 1.03 0 0 0-1.457 0l-1.45 1.45 1.84 1.84a1.223 1.223 0 0 1 1.55 1.56l1.773 1.774a1.224 1.224 0 0 1 1.267 2.025 1.226 1.226 0 0 1-2.002-1.334L8.58 5.963v4.353a1.226 1.226 0 1 1-1.008-.036V5.887a1.226 1.226 0 0 1-.666-1.608L5.093 2.465l-4.79 4.79a1.03 1.03 0 0 0 0 1.457l6.986 6.986a1.03 1.03 0 0 0 1.457 0l6.953-6.953a1.031 1.031 0 0 0 0-1.457"/>
                              </svg>
                            Fork Repo 
                        </a>
                        <a href="https://www.youtube.com/@shantoyt411" target="_blank" class="btn btn-danger w-50 text-light fw-semibold flex justify-center items-center gap-2 text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-youtube" viewBox="0 0 16 16">
                                <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z"/>
                              </svg>
                            Subscribe
                        </a>
                    </div>
                </div>
                <div class="offcanvas-body">
                    <div class="wow animate__animated animate__fadeInLeft ">
                        <h2 class="font-semibold" style="text-shadow: 1px 2px 8px black;">Explore</h2>
                    </div>
                    <div class=" d-block px-5 wow animate__animated animate__fadeInRight ">
                        <a href="https://telegram.me/neil_admin" type="button" class="btn btn-outline-secondary w-100 mt-1 wow animate__animated animate__fadeIn ">Owner</a>
                        <a href="https://telegram.me/neil_admin" type="button" class="btn btn-outline-secondary w-100 mt-1">Telegram</a>
                 
                    </div>
                    
                <!-- section -->
                <div class="m-2 mt-4">
                    <p>
                        <a class="btn btn-primary w-100 flex justify-center items-center gap-2" data-bs-toggle="collapse" href="" role="button" aria-expanded="false" aria-controls="multiCollapseExample1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-envelope-paper-heart" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v1.133l.941.502A2 2 0 0 1 16 5.4V14a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V5.4a2 2 0 0 1 1.059-1.765L2 3.133V2Zm0 2.267-.47.25A1 1 0 0 0 1 5.4v.817l1 .6v-2.55Zm1 3.15 3.75 2.25L8 8.917l1.25.75L13 7.417V2a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v5.417Zm11-.6 1-.6V5.4a1 1 0 0 0-.53-.882L14 4.267v2.55ZM8 2.982C9.664 1.309 13.825 4.236 8 8 2.175 4.236 6.336 1.31 8 2.982Zm7 4.401-4.778 2.867L15 13.117V7.383Zm-.035 6.88L8 10.082l-6.965 4.18A1 1 0 0 0 2 15h12a1 1 0 0 0 .965-.738ZM1 13.116l4.778-2.867L1 7.383v5.734Z"/>
                              </svg>
                            <span class="ps-2">Watch Deploy Tutorial</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-activity" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M6 2a.5.5 0 0 1 .47.33L10 12.036l1.53-4.208A.5.5 0 0 1 12 7.5h3.5a.5.5 0 0 1 0 1h-3.15l-1.88 5.17a.5.5 0 0 1-.94 0L6 3.964 4.47 8.171A.5.5 0 0 1 4 8.5H.5a.5.5 0 0 1 0-1h3.15l1.88-5.17A.5.5 0 0 1 6 2Z"/>
                              </svg>
                        </a>
                    </p>
                </div>
                  
                    <!-- footer of sidebar -->
                    
                    <div class="text-center w-100">
                        <div class="mt-3">
                            <h5 class="flex justify-center items-center gap-2">
                                <span class="text-dark font-monospace" style="text-shadow: 3px 5px 11px rgb(126, 10, 80);">With Love</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" fill="currentColor" class="bi bi-chat-heart" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" fill="#dc0303" d="M2.965 12.695a1 1 0 0 0-.287-.801C1.618 10.83 1 9.468 1 8c0-3.192 3.004-6 7-6s7 2.808 7 6c0 3.193-3.004 6-7 6a8.06 8.06 0 0 1-2.088-.272 1 1 0 0 0-.711.074c-.387.196-1.24.57-2.634.893a10.97 10.97 0 0 0 .398-2Zm-.8 3.108.02-.004c1.83-.363 2.948-.842 3.468-1.105A9.06 9.06 0 0 0 8 15c4.418 0 8-3.134 8-7s-3.582-7-8-7-8 3.134-8 7c0 1.76.743 3.37 1.97 4.6a10.437 10.437 0 0 1-.524 2.318l-.003.011a10.722 10.722 0 0 1-.244.637c-.079.186.074.394.273.362a21.673 21.673 0 0 0 .693-.125ZM8 5.993c1.664-1.711 5.825 1.283 0 5.132-5.825-3.85-1.664-6.843 0-5.132Z"/>
                                  </svg>
                            </h5>
                        </div>
                        <div class="text-center w-100 px-2 flex justify-center items-center gap-2">
                            <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-c-circle " viewBox="0 0 16 16"><path d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM8.146 4.992c-1.212 0-1.927.92-1.927 2.502v1.06c0 1.571.703 2.462 1.927 2.462.979 0 1.641-.586 1.729-1.418h1.295v.093c-.1 1.448-1.354 2.467-3.03 2.467-2.091 0-3.269-1.336-3.269-3.603V7.482c0-2.261 1.201-3.638 3.27-3.638 1.681 0 2.935 1.054 3.029 2.572v.088H9.875c-.088-.879-.768-1.512-1.729-1.512Z"/></svg></span>
                            <span class="font-bold bg-clip-text text-[18px] text-transparent bg-gradient-to-r from-yellow-400  via-orange-700 to-red-700" style="text-shadow: 3px 5px 11px black;">neil_admin</span>
                        </div>
                    </div>

                    <!-- <p>Try scrolling the rest of the page to see this option in action.</p> -->
                </div>
                </div>

                <!-- web title -->
                <div class="container-fluid">
                    <a class="navbar-brand  text-light fw-bold " href="https://t.me/neil_admin">
                        <span class="bg-clip-text sm:text-[25px] text-[22px] text-transparent bg-gradient-to-r from-pink-500 to-violet-500">
                            HOME
                          </span>
                    </a>
                  </div>
               </div>
            </nav>
            <!-- ./Navbar -->
    </header>
    <!-- ./Header -->

    <div class="bg-black  text-light"  >
        <!-- file Name -> to be displayed here  -->
        <div class="justify-content-center align-items-center w-100 d-flex">
            <div id="file-name" class="tex-center text-lg max:w-74  p-2 flex-wrap text-center justify-center items-center">
                <p class="fw-bold"> 
                    <span id="myDiv" class="h6 flex-wrap text-center justify-center items-center card-title wow animate__animated animate__fadeInRight" >
                        {{file_name}}
                    </span>
                </p>
            </div>
        </div>
        <!-- Online Video Player -> to be displayed here  -->
        <div class="justify-content-center align-items-center w-100 d-flex  flex-wrap">
            <div class="lg:w-9/12 lg:px-12 lg:py-4 w-full  border border-danger wow animate__animated animate__fadeInLeft">
                <video id="player" class="player " playsinline controls>
                    <source src="{{file_url}}" type="video/mp4">
                </video>
            </div>
        </div>

        <!-- External video player section -->
            <div class="justify-content-center align-items-center w-100 d-flex">
                    <div class="row d-flex w-100 justify-content-center align-items-center">

                        <div class="col-lg-7 col-12 d-flex justify-content-start align-items-center ">
                            <p class="h4 px-2 mt-4 bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-orange-600 font-bold mb-2">Play with...</p>
                        </div>
                        <div class="col-lg-6 col-10 d-flex justify-center items-center flex-wrap pb-3 gap-2 wow animate__animated animate__fadeIn">
                            <div onclick="vlc_player()" class="card card-body col-lg-3  col-5 bg-dark text-secondary text-center justify-content-center align-items-center hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__fadeInLeft">
                                <svg class="wow animate__animated animate__shakeX  animate__delay-1s" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="50" height="50" viewBox="0 0 64 64"><linearGradient id="d2lfkA6lemjIH1JdWslHIa_74320_gr1" x1="32" x2="32" y1="16.021" y2="39.25" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset=".437" stop-color="#6dc7ff"></stop><stop offset="1" stop-color="#e6abff"></stop></linearGradient><path fill="url(#d2lfkA6lemjIH1JdWslHIa_74320_gr1)" d="M32,31.25c2.891,0,5.255-0.323,6.939-0.712l2.409,7.664C37.765,39.116,33.881,39.25,32,39.25 c-1.88,0-5.765-0.134-9.347-1.049l2.408-7.663C26.745,30.927,29.11,31.25,32,31.25z M32.5,24.25c1.554,0,2.999-0.141,4.343-0.38 l-2.429-7.729c-0.587,0.063-1.211,0.109-1.914,0.109c-1.1,0-2.062-0.092-2.876-0.229l-2.404,7.651 C28.816,24.034,30.574,24.25,32.5,24.25z"></path><linearGradient id="d2lfkA6lemjIH1JdWslHIb_74320_gr2" x1="32.105" x2="32.105" y1="7.415" y2="56" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset="0" stop-color="#1a6dff"></stop><stop offset="1" stop-color="#c822ff"></stop></linearGradient><path fill="url(#d2lfkA6lemjIH1JdWslHIb_74320_gr2)" d="M56.903,50.041l-2.216-10.779c-0.513-2.265-2.52-3.847-4.881-3.847H45.09l-8.059-24.451 c-0.7-2.123-2.726-3.549-5.04-3.549c-2.314,0-4.339,1.426-5.039,3.549l-8.058,24.451h-4.491c-2.362,0-4.369,1.582-4.885,3.866 L7.31,50.021c-0.327,1.442,0.019,2.937,0.947,4.099C9.212,55.314,10.645,56,12.19,56h39.828c1.545,0,2.979-0.685,3.934-1.88 C56.88,52.958,57.226,51.464,56.903,50.041z M19.096,41.19l9.755-29.601c0.429-1.301,1.69-2.175,3.14-2.175s2.711,0.874,3.14,2.175 l9.774,29.655c0.201,0.72,0.088,1.331-0.357,1.923c-1.509,2.001-6.32,3.246-12.557,3.247c-0.004,0-0.007,0-0.011,0 c-6.223,0-11.017-1.229-12.511-3.208C19,42.586,18.886,41.933,19.096,41.19z M54.39,52.872C53.816,53.589,52.952,54,52.019,54H12.19 c-0.934,0-1.798-0.411-2.371-1.128C9.39,52.334,9.181,51.677,9.202,51H17v-2H9.561l0.411-2H14v-2h-3.616l1.089-5.297 c0.3-1.326,1.532-2.288,2.93-2.288h3.832l-1.05,3.188c-0.392,1.376-0.154,2.693,0.689,3.81c1.921,2.544,7.062,4.003,14.106,4.003 c0.003,0,0.008,0,0.011,0c7.06-0.002,12.219-1.476,14.153-4.044c0.823-1.093,1.058-2.37,0.678-3.694 c-0.003-0.013-0.007-0.025-0.011-0.038l-1.062-3.224h4.058c1.397,0,2.63,0.962,2.926,2.269L53.825,45H50v2h4.236l0.411,2H47v2h8.008 C55.029,51.671,54.821,52.332,54.39,52.872z"></path></svg>
                                VLC Player
                            </div>
                            <div onclick="mx_player()" class="card card-body col-lg-3  col-5 bg-dark text-secondary text-center justify-content-center align-items-center hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__fadeInRight">
                                <svg class="wow animate__animated animate__shakeX animate__delay-2s" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="50" height="50" viewBox="0 0 64 64"><circle cx="32" cy="32" r="28" fill="#ffeb9b"></circle><circle cx="32" cy="32" r="21" fill="#f9dd8f"></circle><path fill="#8d6c9f" d="M32,6c14.336,0,26,11.664,26,26S46.336,58,32,58S6,46.336,6,32S17.664,6,32,6 M32,4	C16.536,4,4,16.536,4,32s12.536,28,28,28s28-12.536,28-28S47.464,4,32,4L32,4z"></path><path fill="#fff8ee" d="M42.5,31.134l-17-9.815c-0.667-0.385-1.5,0.096-1.5,0.866v19.63c0,0.77,0.833,1.251,1.5,0.866 l17-9.815C43.167,32.481,43.167,31.519,42.5,31.134z"></path><path fill="#8d6c9f" d="M25,43.818c-0.344,0-0.688-0.09-1-0.271c-0.626-0.361-1-1.009-1-1.732v-19.63 c0-0.723,0.374-1.371,1-1.732c0.623-0.362,1.371-0.363,2,0l17,9.815v0c0.626,0.361,1,1.009,1,1.732s-0.374,1.371-1,1.732l-17,9.815 C25.687,43.728,25.343,43.818,25,43.818z M25,22.185v19.63L42,32l0.5-0.866L42,32L25,22.185z"></path><g><path fill="#8d6c9f" d="M53.596,37.162c-0.063,0-0.126-0.006-0.189-0.018l-1.964-0.376 c-0.542-0.104-0.897-0.628-0.794-1.17c0.104-0.542,0.627-0.898,1.171-0.793l1.964,0.376c0.542,0.104,0.897,0.628,0.794,1.17 C54.485,36.829,54.066,37.162,53.596,37.162z"></path><path fill="#8d6c9f" d="M51.997,42.133c-0.139,0-0.28-0.029-0.415-0.091l-1.819-0.832 c-0.502-0.229-0.723-0.823-0.493-1.325c0.229-0.501,0.82-0.726,1.325-0.493l1.819,0.832c0.502,0.229,0.723,0.823,0.493,1.325 C52.739,41.916,52.377,42.133,51.997,42.133z"></path><path fill="#8d6c9f" d="M49.278,46.59c-0.216,0-0.435-0.07-0.618-0.214l-1.571-1.237c-0.434-0.342-0.508-0.97-0.167-1.404 c0.343-0.433,0.97-0.51,1.405-0.167l1.571,1.237c0.434,0.342,0.508,0.97,0.167,1.404C49.867,46.459,49.574,46.59,49.278,46.59z"></path><path fill="#8d6c9f" d="M45.591,50.28c-0.296,0-0.589-0.131-0.787-0.381l-1.237-1.571 c-0.341-0.434-0.267-1.063,0.167-1.404c0.436-0.343,1.063-0.266,1.405,0.167l1.237,1.571c0.341,0.434,0.267,1.063-0.167,1.404 C46.025,50.21,45.807,50.28,45.591,50.28z"></path><path fill="#8d6c9f" d="M41.134,52.998c-0.38,0-0.742-0.217-0.91-0.584l-0.832-1.819c-0.229-0.502-0.009-1.096,0.493-1.325 c0.504-0.233,1.096-0.008,1.325,0.493l0.832,1.819c0.229,0.502,0.009,1.096-0.493,1.325 C41.414,52.969,41.272,52.998,41.134,52.998z"></path><path fill="#8d6c9f" d="M36.161,54.595c-0.47,0-0.89-0.333-0.981-0.812l-0.377-1.964c-0.104-0.542,0.252-1.066,0.794-1.17 c0.54-0.105,1.066,0.251,1.171,0.793l0.377,1.964c0.104,0.542-0.252,1.066-0.794,1.17C36.287,54.589,36.224,54.595,36.161,54.595z"></path></g></svg>
                                Mx Player
                            </div>
                            <div onclick="playit_player()" class="card card-body col-lg-3  col-5 bg-dark text-secondary text-center justify-content-center align-items-center hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__fadeInLeft">
                                <svg class="wow animate__animated animate__shakeX  animate__delay-1s " xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 48 48" id="b"><defs><style>.c{fill:none;stroke:#22abbd;stroke-linecap:round;stroke-linejoin:round;}</style></defs><path class="c" d="m17,32.5v5c0,2.7614-2.2386,5-5,5h0c-2.7614,0-5-2.2386-5-5"/><path class="c" d="m7,37.5c0-2.7614,2.2386-5,5-5h25c2.2,0,4-1.8,4-4V9.5c0-2.2-1.8-4-4-4H11c-2.2,0-4,1.8-4,4v28"/><path class="c" d="m29.1876,18.467l-9.1448-5.2652c-.822-.4733-1.8482.12-1.8482,1.0685v10.5304c0,.9485,1.0262,1.5418,1.8482,1.0685l9.1448-5.2652c.8237-.4742.8237-1.6628,0-2.137Z"/></svg>
                                Play-It
                            </div>
                            <div onclick="km_player()" class="card card-body col-lg-3  col-5 bg-dark text-secondary text-center jcc align-items-center hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__fadeInRight">
                                <svg  class="wow animate__animated animate__shakeX  animate__delay-2s " xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64" width="50px" height="50px"><defs><rect id="zOt10NeCJpLJlmsRZOsAFa" width="280.487" height="124" x="654" y="-72"/></defs><clipPath id="zOt10NeCJpLJlmsRZOsAFb"><use overflow="visible" xlink:href="#zOt10NeCJpLJlmsRZOsAFa"/></clipPath><g><linearGradient id="zOt10NeCJpLJlmsRZOsAFc" x1="76.124" x2="76.124" y1="12.7" y2="50.192" gradientTransform="matrix(-1 0 0 1 115.415 0)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#1a6dff"/><stop offset="1" stop-color="#c822ff"/></linearGradient><path fill="url(#zOt10NeCJpLJlmsRZOsAFc)" d="M50.999,50.192c-0.597,0-1.19-0.179-1.702-0.531L25.882,33.513 c-0.824-0.568-1.31-1.505-1.298-2.506c0.013-1,0.52-1.925,1.356-2.474l23.416-15.342c0.923-0.604,2.099-0.655,3.07-0.129 C53.397,13.587,54,14.598,54,15.701v31.49c0,1.118-0.615,2.136-1.605,2.656C51.955,50.078,51.476,50.192,50.999,50.192z M50.992,14.697c-0.162,0-0.348,0.042-0.54,0.168L27.036,30.207c-0.283,0.186-0.448,0.485-0.452,0.824 c-0.004,0.338,0.154,0.643,0.433,0.835l23.415,16.148c0.45,0.311,0.872,0.146,1.033,0.062C51.8,47.9,52,47.569,52,47.191v-31.49 c0-0.538-0.367-0.794-0.524-0.88C51.385,14.772,51.21,14.697,50.992,14.697z"/><linearGradient id="zOt10NeCJpLJlmsRZOsAFd" x1="23.708" x2="23.708" y1="13.698" y2="49.195" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6dc7ff"/><stop offset="1" stop-color="#e6abff"/></linearGradient><path fill="url(#zOt10NeCJpLJlmsRZOsAFd)" d="M10,15.701v31.49c0,1.612,1.809,2.561,3.135,1.646l23.416-16.149 c1.169-0.806,1.149-2.541-0.039-3.319L13.096,14.028C11.766,13.157,10,14.111,10,15.701z"/><g><linearGradient id="zOt10NeCJpLJlmsRZOsAFe" x1="24.708" x2="24.708" y1="12.7" y2="50.192" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#1a6dff"/><stop offset="1" stop-color="#c822ff"/></linearGradient><path fill="url(#zOt10NeCJpLJlmsRZOsAFe)" d="M13.001,50.192c-0.477,0-0.956-0.114-1.396-0.345C10.615,49.327,10,48.31,10,47.191v-31.49 c0-1.104,0.603-2.114,1.573-2.639c0.972-0.526,2.147-0.476,3.07,0.129L38.06,28.533c0.837,0.549,1.344,1.474,1.356,2.474 c0.012,1.001-0.474,1.938-1.298,2.506L14.703,49.661C14.191,50.014,13.598,50.192,13.001,50.192z M13.008,14.697 c-0.218,0-0.393,0.075-0.483,0.124C12.367,14.907,12,15.163,12,15.701v31.49c0,0.378,0.2,0.709,0.535,0.886 c0.161,0.084,0.583,0.25,1.033-0.062l23.415-16.148c0.278-0.192,0.437-0.497,0.433-0.835c-0.004-0.339-0.169-0.639-0.452-0.824 L13.548,14.865C13.355,14.739,13.17,14.697,13.008,14.697z"/></g></g></svg>
                                Km Player
                            </div>
                            <div onclick="s_player()" class="card card-body col-lg-3  col-5 bg-dark text-secondary text-center jcc align-items-center hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__fadeInRight">
                                <svg class="wow animate__animated animate__shakeX  animate__delay-3s " xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="50" height="50" viewBox="0 0 64 64">
                                    <linearGradient id="u3DU_xVfwAA7Ehald_UwAa_79075_gr1" x1="35.5" x2="35.5" y1="6.999" y2="57" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset="0" stop-color="#1a6dff"></stop><stop offset="1" stop-color="#c822ff"></stop></linearGradient><path fill="url(#u3DU_xVfwAA7Ehald_UwAa_79075_gr1)" d="M56.7,30c-0.95-0.5-9.83-5.54-9.92-5.6L16.69,7.42c-0.91-0.51-1.89-0.56-2.62-0.13 C13.39,7.69,13,8.44,13,9.34v45.57c0,0.81,0.35,1.47,0.96,1.83c0.3,0.17,0.63,0.26,0.98,0.26c0.42,0,0.86-0.12,1.29-0.37 l40.43-22.81c1.18-0.67,1.35-1.52,1.34-1.97C57.99,31.11,57.52,30.43,56.7,30z M44.59,25.46l-5.18,5.03L19.82,11.48L44.59,25.46z M15,54.2V9.6l22.98,22.29L15,54.2z M19.81,52.31l19.6-19.03l5.2,5.04L19.81,52.31z M55.67,32.08l-9.24,5.22l-5.58-5.41l5.56-5.4 c2.04,1.16,8.52,4.84,9.36,5.28c0.08,0.04,0.14,0.08,0.18,0.12C55.89,31.94,55.81,32,55.67,32.08z"></path><linearGradient id="u3DU_xVfwAA7Ehald_UwAb_79075_gr2" x1="26.055" x2="26.055" y1="14.33" y2="49.47" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset="0" stop-color="#6dc7ff"></stop><stop offset="1" stop-color="#e6abff"></stop></linearGradient><path fill="url(#u3DU_xVfwAA7Ehald_UwAb_79075_gr2)" d="M35.11 31.89L17 49.47 17 14.33z"></path>
                                </svg>
                                S Player
                            </div>

			                <div onclick="hd_player()" class="card card-body col-lg-3  col-5 bg-dark text-secondary text-center jcc align-items-center hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__fadeInRight">
                                <svg class="wow animate__animated animate__shakeX  animate__delay-3s " xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="50px" width="50px" version="1.1" id="Layer_1" viewBox="0 0 512 512" xml:space="preserve">
                                    <polygon style="fill:#FFFFFF;" points="187.368,146.928 187.368,355.8 382.992,251.368 "/>
                                    <path style="fill:red;" d="M256,0.376C114.616,0.376,0,114.824,0,256s114.616,255.624,256,255.624S512,397.176,512,256  S397.384,0.376,256,0.376z M184.496,146.928l195.624,104.44L184.496,355.8V146.928z"/>
                                </svg>
                                HD Player(4K)
                            </div>
                            <div onclick="Download()" class="col-lg-4 p-2 col-12 rounded-3xl bg-dark text-white text-center align-items-center hover:bg-gradient-to-r from-pink-500 to-violet-500 hover:shadow-md hover:shadow-gray-100 hover:translate-y-[-5px] transition transition-all duration-300 wow animate__animated animate__rubberBand">
                                Download Now
                            </div>
                        </div>
                </div>
            </div>
            <div class="justify-content-center align-items-center w-100 d-flex mb-3">
                <div class="row d-flex w-100 justify-content-center align-items-center">
                    <div class="col-lg-8 col-10  py-2 text-center bg-danger rounded-4 text-sm wow animate__animated animate__FadeIn">
                            Video Not Playing ? Possibly Because Your Browser Or The Player Doesn't Support Codec Please Download And Watch.
                    </div>
                </div>
            </div>
            <div class="justify-content-center align-items-center w-100 d-flex mb-3">
                <div class="row d-flex w-100 justify-content-center align-items-center">
                    <div class="col-lg-5 col-10  py-2 text-center bg-success rounded-4 text-sm wow animate__animated animate__FadeIn">
                        If You Find Any Broken Links Then Please Contact <a href="https://telegram.me/neil_admin" class="text-[16px] font-bold underline active:text-blue-600">neil_admin</a>
                    </div>
                </div>
            </div>

    </div>

        <footer class="bg-dark justify-content-around align-items-center">
            <div class="container-fluid  justify-content-center align-itemsd x. -center">
                <!-- Section: Social media -->
                <section class="lg:p-4 p-2 justify-content-center align-items-center text-center ">  
                    <!-- YouTube -->
                    <a class="btn btn-outline-light btn-floating m-1 rounded-circle p-2 border-secondary wow animate__animated animate__fadeInLeft hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-200" href="https://youtube.com/@shantoyt411" target="_blank" role="button">
                         <svg class="wow animate__animated animate__heartBeat animate__delay-1s animate__infinite" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="30" height="30" viewBox="0 0 64 64">
                            <linearGradient id="PMWUiMSnUf2R_PPJ6rqZea_44112_gr1" x1="32" x2="32" y1="10" y2="54.751" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset="0" stop-color="#1a6dff"></stop><stop offset="1" stop-color="#c822ff"></stop></linearGradient><path fill="url(#PMWUiMSnUf2R_PPJ6rqZea_44112_gr1)" d="M32,53.001c-6.358,0-12.716-0.195-19.299-0.584c-4.96-0.293-8.961-4.242-9.308-9.187 c-0.521-7.452-0.521-15.009-0.001-22.46c0.348-4.945,4.349-8.895,9.309-9.188c13.167-0.778,25.433-0.778,38.598,0 c4.96,0.293,8.961,4.242,9.308,9.187c0.521,7.452,0.521,15.009,0.001,22.46c-0.348,4.945-4.349,8.895-9.309,9.188l0,0 C44.716,52.806,38.358,53.001,32,53.001z M51.182,50.421l0.059,0.998L51.182,50.421c3.959-0.234,7.153-3.386,7.431-7.331 c0.515-7.358,0.515-14.821-0.001-22.181c-0.276-3.944-3.471-7.096-7.43-7.33c-13.088-0.773-25.275-0.773-38.363,0 c-3.959,0.234-7.153,3.386-7.431,7.331c-0.515,7.358-0.515,14.821,0.001,22.181c0.276,3.944,3.471,7.096,7.43,7.33 C25.906,51.194,38.094,51.194,51.182,50.421z"></path><linearGradient id="PMWUiMSnUf2R_PPJ6rqZeb_44112_gr2" x1="31.72" x2="31.72" y1="24.25" y2="39.252" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset="0" stop-color="#6dc7ff"></stop><stop offset="1" stop-color="#e6abff"></stop></linearGradient><path fill="url(#PMWUiMSnUf2R_PPJ6rqZeb_44112_gr2)" d="M25 39.84L25 24.16 38.44 32z"></path><linearGradient id="PMWUiMSnUf2R_PPJ6rqZec_44112_gr3" x1="32.212" x2="32.212" y1="10" y2="54.751" gradientUnits="userSpaceOnUse" spreadMethod="reflect"><stop offset="0" stop-color="#1a6dff"></stop><stop offset="1" stop-color="#c822ff"></stop></linearGradient><path fill="url(#PMWUiMSnUf2R_PPJ6rqZec_44112_gr3)" d="M24,41.581V22.419L40.425,32L24,41.581z M26,25.901v12.197L36.456,32L26,25.901z"></path>
                        </svg>
                    </a>
              
                    <!-- Telegram -->
                    <a class="btn btn-outline-light btn-floating m-1 rounded-circle p-2 border-secondary wow animate__animated animate__fadeInDown animate__delay-1s hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-200" href="https://telegram.me/neil_admin" target="_blank" role="button">
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="30" height="30" viewBox="0 0 50 50">
                        <path fill="url(#PMWUiMSnUf2R_PPJ6rqZea_44112_gr1)" d="M25,2c12.703,0,23,10.297,23,23S37.703,48,25,48S2,37.703,2,25S12.297,2,25,2z M32.934,34.375	c0.423-1.298,2.405-14.234,2.65-16.783c0.074-0.772-0.17-1.285-0.648-1.514c-0.578-0.278-1.434-0.139-2.427,0.219	c-1.362,0.491-18.774,7.884-19.78,8.312c-0.954,0.405-1.856,0.847-1.856,1.487c0,0.45,0.267,0.703,1.003,0.966	c0.766,0.273,2.695,0.858,3.834,1.172c1.097,0.303,2.346,0.04,3.046-0.395c0.742-0.461,9.305-6.191,9.92-6.693	c0.614-0.502,1.104,0.141,0.602,0.644c-0.502,0.502-6.38,6.207-7.155,6.997c-0.941,0.959-0.273,1.953,0.358,2.351	c0.721,0.454,5.906,3.932,6.687,4.49c0.781,0.558,1.573,0.811,2.298,0.811C32.191,36.439,32.573,35.484,32.934,34.375z"></path>
                    </svg>
                    </a>
              
                    <!-- Instagram -->
                    <a class="btn btn-outline-light btn-floating m-1 rounded-circle p-2 border-secondary wow animate__animated animate__fadeInUp animate__delay-2s hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300" href="https://facebook.com/ahmed.neil.502683" target="_blank" role="button">
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="30" height="30" viewBox="0 0 50 50">
                        <path fill="url(#PMWUiMSnUf2R_PPJ6rqZea_44112_gr1)" d="M 16 3 C 8.8324839 3 3 8.8324839 3 16 L 3 34 C 3 41.167516 8.8324839 47 16 47 L 34 47 C 41.167516 47 47 41.167516 47 34 L 47 16 C 47 8.8324839 41.167516 3 34 3 L 16 3 z M 16 5 L 34 5 C 40.086484 5 45 9.9135161 45 16 L 45 34 C 45 40.086484 40.086484 45 34 45 L 16 45 C 9.9135161 45 5 40.086484 5 34 L 5 16 C 5 9.9135161 9.9135161 5 16 5 z M 37 11 A 2 2 0 0 0 35 13 A 2 2 0 0 0 37 15 A 2 2 0 0 0 39 13 A 2 2 0 0 0 37 11 z M 25 14 C 18.936712 14 14 18.936712 14 25 C 14 31.063288 18.936712 36 25 36 C 31.063288 36 36 31.063288 36 25 C 36 18.936712 31.063288 14 25 14 z M 25 16 C 29.982407 16 34 20.017593 34 25 C 34 29.982407 29.982407 34 25 34 C 20.017593 34 16 29.982407 16 25 C 16 20.017593 20.017593 16 25 16 z"></path>
                        </svg>
                    </a>
              
                    <!-- ADMIN -->
                    <a class="btn btn-outline-light btn-floating m-1 rounded-circle p-2 border-secondary wow animate__animated animate__fadeInDown animate__delay-1s hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300" href="https://telegram.me/neil_admin" target="_blank" role="button">
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="30" height="30" viewBox="0 0 30 30">
                        <path fill="url(#PMWUiMSnUf2R_PPJ6rqZea_44112_gr1)" d="M24,4H6C4.895,4,4,4.895,4,6v18c0,1.105,0.895,2,2,2h18c1.105,0,2-0.895,2-2V6C26,4.895,25.105,4,24,4z M10.954,22h-2.95 v-9.492h2.95V22z M9.449,11.151c-0.951,0-1.72-0.771-1.72-1.72c0-0.949,0.77-1.719,1.72-1.719c0.948,0,1.719,0.771,1.719,1.719 C11.168,10.38,10.397,11.151,9.449,11.151z M22.004,22h-2.948v-4.616c0-1.101-0.02-2.517-1.533-2.517 c-1.535,0-1.771,1.199-1.771,2.437V22h-2.948v-9.492h2.83v1.297h0.04c0.394-0.746,1.356-1.533,2.791-1.533 c2.987,0,3.539,1.966,3.539,4.522V22z"></path>
                    </svg>
                    </a>
              
                    <!-- Github -->
                    <a class="btn btn-outline-light btn-floating m-1 rounded-circle p-2 border-secondary wow animate__animated animate__fadeInUp animate__delay-2s hover:shadow-md hover:shadow-gray-400 hover:translate-y-[-5px] transition transition-all duration-300" href="https://github.com/neil" target="_blank" role="button">
                     <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="30" height="30" viewBox="0 0 30 30">
                        <path  fill="url(#PMWUiMSnUf2R_PPJ6rqZea_44112_gr1)" d="M15,3C8.373,3,3,8.373,3,15c0,5.623,3.872,10.328,9.092,11.63C12.036,26.468,12,26.28,12,26.047v-2.051 c-0.487,0-1.303,0-1.508,0c-0.821,0-1.551-0.353-1.905-1.009c-0.393-0.729-0.461-1.844-1.435-2.526 c-0.289-0.227-0.069-0.486,0.264-0.451c0.615,0.174,1.125,0.596,1.605,1.222c0.478,0.627,0.703,0.769,1.596,0.769 c0.433,0,1.081-0.025,1.691-0.121c0.328-0.833,0.895-1.6,1.588-1.962c-3.996-0.411-5.903-2.399-5.903-5.098 c0-1.162,0.495-2.286,1.336-3.233C9.053,10.647,8.706,8.73,9.435,8c1.798,0,2.885,1.166,3.146,1.481C13.477,9.174,14.461,9,15.495,9 c1.036,0,2.024,0.174,2.922,0.483C18.675,9.17,19.763,8,21.565,8c0.732,0.731,0.381,2.656,0.102,3.594 c0.836,0.945,1.328,2.066,1.328,3.226c0,2.697-1.904,4.684-5.894,5.097C18.199,20.49,19,22.1,19,23.313v2.734 c0,0.104-0.023,0.179-0.035,0.268C23.641,24.676,27,20.236,27,15C27,8.373,21.627,3,15,3z"></path>
                    </svg>
                    </a>
                  </section>

                  <!-- For Tech_VJ & neil_admin // credit --- Please don't remove footer credit -->
                  <div class="row flex-row-reverse text-secondary justify-content-center align-items-center">
                      <div class="col-lg-6 col-12 justify-content-center align-items-center text-center p-1 wow animate__animated animate__fadeInRight">
                        <ul class="navbar-nav">
                            <li class="navbar-item bg-gradient-to-r from-pink-500 rounded-xl p-2"> <h5 class="text-gray-100 font-semibold text-sm"> Think & Just Code It... </h5><sup class="badge badge-error text-xs text-gray-300 font-monospace">-neil_admin</sup> </li>
                        </ul>
                    </div>
                    <div class="col-lg-6 col-12 justify-content-center align-items-center text-center p-1 wow animate__animated animate__fadeInLeft">
                        <ul class="navbar-nav">
                            <li class="navbar-item bg-gradient-to-l from-pink-500 rounded-xl p-2 "> <h5 class="text-gray-100 font-semibold text-sm" > Success Is The Journey Of Millions Of Failure And Lots Of Hard Work</h5> <sup class="badge  text-xs text-gray-300 font-monospace">-neil_admin</sup> </li>
                        </ul>
                    </div>
                    <div class="col-lg-2 col-4 justify-content-center align-items-center text-center mt-lg-0 mt-5  pt-lg-0 pt-4 wow animate__animated animate__fadeInUp">
                        <ul class="navbar-nav ">
                            <li class="nav-item justify-center items-center flex"  style="margin-top: -40px;">
                                <img src="https://i.ibb.co.com/Qw2PbsF/cinepix.png" height="140px" class="rounded-3 " alt="logo">
                        </li>
                        <!-- <li class="nav-item justify-center items-center text-center">
                            <a  class="nav-link h1 text-center sm:-m-1 -ms-4"><h5><a class="font-bold" href="https://t.me/neil_admin" target="_blank">Movies : </a>Run and managed by <a href="https://telegram.me/neil_admin" target="_blank" class="bg-clip-text text-[16px] text-transparent bg-gradient-to-r from-orange-500 to-pink-500">Adil</a> & <a href="https://telegram.me/neil_admin" target="_blank">Indu(<a href="https://telegram.me/neil_admin" target="_blank" class="bg-clip-text text-[16px] text-transparent bg-gradient-to-r from-orange-500 to-pink-500">neil_admin</a>)</a> </h5></a>
                        </li> -->
                    </ul>
                </div>
                
                <!-- Copyright -->
            </div> 
            <div class=" justify-center items-center text-center w-full text-center flex ">
                <div  class="text-gray-400 text-center sm:-m-1 -ms-4 m-2 p-2">
                    <h5>
                        
                    </h5>
                </div>
            </div>
        </div>
        <hr>
        <div class="text-center text-xs p-3 dark:text-light text-light gap-2" style="background-color: rgba(0, 0, 0, 0.2);">
                <!-- For Tech_VJ & neil_admin // credit --- Please don't remove footer credit -->
            ©  <script type="text/JavaScript">
                var theDate=new Date() 
                    document.write(theDate.getFullYear()) 
            </script> Copyright :
                <a class="bg-clip-text text-[16px] text-transparent bg-gradient-to-r from-pink-500 to-violet-500 font-bold tracking-wide px-1" href="https://telegram.me/neil_admin">ADMIN</a>
                
                All Right Reserved
            </div>   
        </footer>


    <script src="js/wow.min.js"></script>
    <script>
        new WOW().init();
    </script>
    <script>
        var div = document.getElementById('myDiv');
        var text = div.textContent;

        if(text.length > 60){
            div.textContent = text.slice(0, 60)+"....";
        }

    </script>
    
    <script src="https://cdn.plyr.io/3.6.9/plyr.js"></script>
	<script>
        const controls = [
            'play-large',
            'rewind','play', 
            'fast-forward', 
            'progress', 
            'current-time',
            'duration',
            'mute',
            'volume',
            'captions',
            'settings',
            'pip',
            'airplay',
            'download',
            'fullscreen'
        ];
        document.addEventListener('DOMContentLoaded', () => {
            const player = Plyr.setup('.player', { controls });
        });

    </script>
    <!-- iterate animation -->




    <!-- disabling right click -->
    <script>
        document.addEventListener("contextmenu", function (e) {
        e.preventDefault();
    });
    document.addEventListener('keydown', function (e) {
        if (
            e.key === 'F12' ||
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.key === 'u') ||
            e.ctrlKey ||
            e.shiftKey ||
            e.altKey
        ) {
            e.preventDefault();
        }
    });
    </script>
    
    
    
    <script>
        const videolink = window.location.href;
        const  Link = videolink.replace("/watch/", "/");

        function vlc_player() {
            const openLink = Link;
            const openVlc = `vlc://${openLink}`;
            window.location.href = openVlc;
        }
	    
	function mx_player() {
            const openLink = Link;
            const openMx = `intent:${openLink}#Intent;package=com.mxtech.videoplayer.ad;end`;
            window.location.href = openMx;
        }
        
	function playit_player() {
    	const openLink = Link;
    	const openPlayit = `playit://playerv2/video?url=${openLink}`;
   	 window.location.href = openPlayit;
	}
	    
	function s_player() {
            const openLink = Link;
            const openSplayer = `intent:${openLink}#Intent;action=com.young.simple.player.playback_online;package=com.young.simple.player;end`;
            window.location.href = openSplayer;
        }
	    
	function km_player() {
           const openLink = Link;
           const openKmplayer = `intent:${openLink}#Intent;package=com.kmplayer;end`;
           window.location.href = openKmplayer;
        }
        
	function hd_player() {
           const openLink = Link;
           const openHDplayer = `intent:${openLink}#Intent;package=uplayer.video.player;end`;
           window.location.href = openHDplayer;
        }
	    
        function Download() {
            const openLink = Link;
            window.location.href = openLink;
        }

    </script>


</body>
</html>

<!--  Don't Remove Credit @VJ_Bot Subscribe YouTube Channel For Amazing Bot @Tech_VJ Ask Doubt on telegram @neil_admin -->
