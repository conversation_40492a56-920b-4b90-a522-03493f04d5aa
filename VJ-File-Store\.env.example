# Bot Information
API_ID=your_api_id
API_HASH=your_api_hash
BOT_TOKEN=your_bot_token
BOT_USERNAME=YourBotUsername
PORT=8084

# Admin Configuration
ADMINS=your_user_id

# Database Configuration
DB_URI=your_mongodb_connection_string
DB_NAME=your_database_name

# Clone Mode Configuration (Optional)
CLONE_MODE=False
CLONE_DB_URI=
CDB_NAME=clonetechvj

# Channel Configuration
LOG_CHANNEL=your_log_channel_id

# Auto Delete Configuration
AUTO_DELETE_MODE=True
AUTO_DELETE=10
AUTO_DELETE_TIME=600

# File Store Configuration
PUBLIC_FILE_STORE=True
CUSTOM_FILE_CAPTION=📁 **File Name:** {file_name}\n📊 **Size:** {file_size}\n⏰ **Duration:** {duration}

# Verify Mode (Optional)
VERIFY_MODE=False
SHORTLINK_URL=
SHORTLINK_API=
VERIFY_TUTORIAL=

# Website URL Mode
WEBSITE_URL_MODE=True
WEBSITE_URL=https://your-codespace-url.github.dev

# Stream Mode Configuration
STREAM_MODE=True
SLEEP_THRESHOLD=60
PING_INTERVAL=1200
URL=https://your-codespace-url.github.dev/

# Bot Pictures
PICS=https://graph.org/file/ce1723991756e48c35aa1.jpg
