{"name": "TG File Share/Sharing Bot", "description": "file sharing bot store posts and it can access by special links", "keywords": ["telegram", "file", "sharing"], "repository": "https://github.com/Codeflix-Bots/FileStore", "logo": "https://graph.org/file/6ceef6a98b82b0e0e2f03.jpg", "env": {"TG_BOT_TOKEN": {"description": "Your <PERSON><PERSON> token, Get it from @Botfather", "value": ""}, "OWNER_ID": {"description": "An integer of consisting of your owner ID", "value": "6497757690"}, "APP_ID": {"description": "your app id, take it from my.telegram.org", "value": ""}, "DATABASE_URL": {"description": "Paste your mongo db url", "value": "url"}, "DATABASE_NAME": {"description": "Enter your DATABASE_NAME ", "value": "Cluster0"}, "API_HASH": {"description": "your api hash, take it from my.telegram.org", "value": ""}, "CHANNEL_ID": {"description": "make a channel (database channel), then make the bot as admin in channel, and it's id", "value": "-1001995978690"}, "FORCE_SUB_CHANNEL": {"description": "id of the channel or group, if you want enable force sub feature else put 0", "value": "-1001473043276"}, "FORCE_SUB_CHANNEL2": {"description": "id of the channel or group, if you want enable force sub feature else put 0", "value": "-1001495022147"}, "ADMINS": {"description": "A space separated list of user_ids of Admins, they can only create links", "value": "5115691197 6273945163 6103092779 5231212075", "required": false}, "PROTECT_CONTENT": {"description": "Protect contents from getting forwarded", "value": "False", "required": false}}, "buildpacks": [{"url": "heroku/python"}], "formation": {"worker": {"quantity": 1, "size": "eco"}}}